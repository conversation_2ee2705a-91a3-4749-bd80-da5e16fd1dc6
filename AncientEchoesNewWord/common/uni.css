.uni-padding-wrap {
  padding: 0 15px;
}

.uni-title {
  padding: 10px 0;
}

.uni-title-text {
  font-size: 15px;
  font-weight: bold;
}

.uni-subtitle-text {
  color: #888;
  font-size: 12px;
  font-weight: bold;
  margin-top: 5px;
}

.uni-common-mb {
  margin-bottom: 15px;
}

.uni-common-pb {
  padding-bottom: 15px;
}

.uni-common-pl {
  padding-left: 15px;
}

.uni-common-mt {
  margin-top: 15px;
}

.uni-hello-text {
  color: #7A7E83;
  line-height: 22px;
}

.uni-list {
  background-color: #FFFFFF;
  position: relative;
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid #c8c7cc;
}

.uni-list-cell {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.uni-list-cell-padding {
  padding: 10px 15px;
}

.uni-list-cell-line {
  border-bottom: 1px solid #c8c7cc;
}

.uni-list-cell-hover {
  background-color: #eee;
}

.uni-list-cell-pd {
  padding: 11px 15px 11px 0;
}

.uni-list-cell-left {
  padding: 0 15px 0 10px;
}

.uni-list-cell-db,
.uni-list-cell-right {
  flex: 1;
}

.uni-list-cell-db-text {
  width: 100%;
  /* #ifdef WEB || MP */
  word-break: break-all;
  /* #endif */
}

.uni-label {
  width: 105px;
}

.uni-input {
  height: 25px;
  padding: 8px 13px;
  /* line-height: 25px; */
  font-size: 14px;
  background: #FFF;
  flex: 1;
  box-sizing: content-box;
}

.uni-flex {
  /* #ifdef WEB || MP */
  display: flex;
  /* #endif */
  flex-direction: row;
}

.uni-flex-item {
  flex: 1;
}

.uni-row {
  flex-direction: row;
}

.uni-column {
  flex-direction: column;
}

.uni-bg-red {
  background: #F76260;
}

.uni-bg-green {
  background: #09BB07;
}

.uni-bg-blue {
  background: #007AFF;
}

.uni-btn-v {
  padding: 5px 0;
}

.uni-btn {
  margin-top: 10px;
}

.uni-link {
  color: #576B95;
  font-size: 13px;
}

.uni-center {
  flex-direction: row;
  justify-content: center;
}

.uni-textarea {
  padding: 9px;
  line-height: 1.6;
  font-size: 14px;
}


.uni-icon-size {
  width: 14px;
  height: 14px;
}

.uni-container {
  padding: 15px;
  background-color: #f8f8f8;
}

.uni-header-logo {
  padding: 15px 15px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 5px;
}

.uni-header-image {
  width: 80px;
  height: 80px;
}

.uni-text-box {
  margin-bottom: 20px;
}

.hello-text {
  color: #7A7E83;
  font-size: 14px;
  line-height: 20px;
}


.uni-panel {
  margin-bottom: 12px;
}

.text-disabled {
  color: #a0a0a0 !important;
}


/* --tab-bar-begin-- */

.uni-panel-h {
  background-color: #ffffff;
  flex-direction: row !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 12px;
}

/*
.uni-panel-h:active {
    background-color: #f8f8f8;
}
 */
.uni-panel-h-on {
  background-color: #f0f0f0;
}

.uni-panel-text {
  color: #000000;
  font-size: 14px;
  font-weight: normal;
}

.uni-navigate-item {
  flex-direction: row;
  align-items: center;
  background-color: #FFFFFF;
  border-top-style: solid;
  border-top-color: #f0f0f0;
  border-top-width: 1px;
  padding: 12px;
  justify-content: space-between;
  /* #ifdef WEB */
  /* cursor: pointer; */
  /* #endif */
}

.uni-navigate-item:active {
  background-color: #f8f8f8;
}

.is--active {
  background-color: #f8f8f8;
}

.uni-navigate-text {
  color: #000000;
  font-size: 14px;
  font-weight: normal;
}

/* left-windows */
.left-win-active {
  color: #007AFF !important;
}

/* --tab-bar-end-- */

/* #ifdef APP */
.page-scroll-view {
  flex: 1;
}
/* #endif */

/* web端适配暗黑主题 */
/* #ifdef WEB */
@media (prefers-color-scheme: dark) {
  body,
  uni-page-body {
    color: rgba(0, 0, 0, 0.9);
  }
  .uni-list {
    background-color: #646464;
  }
  .uni-list-cell-db, .uni-radio-wrapper{
    color: #ffffff;
  }
  .uni-container {
    background-color: #1a1a1a;
    color: #ffffff;
  }
}

.uni-container.dark-mode :deep(.uni-collapse-item .uni-collapse-item__title),
.uni-container.dark-mode .uni-navigate-item,
.uni-container.dark-mode .uni-panel-h {
  background-color: #2d2d2d;
  border-bottom-color: #3d3d3d;
}

.uni-container.dark-mode :deep(.uni-collapse-item .uni-collapse-item__title),
.uni-container.dark-mode .uni-navigate-item {
  padding: 12px 18px;
}

.uni-container.dark-mode :deep(.uni-collapse-item .uni-collapse-item__title-text),
.uni-container.dark-mode .hello-text,
.uni-container.dark-mode .uni-navigate-text,
.uni-container.dark-mode .uni-panel-text {
  color: #ffffff;
}

.uni-container.dark-mode .text-disabled {
  color: #666666 !important;
}

/* #endif */


/* #ifdef MP */
page {
  color: rgba(0, 0, 0, 0.9);
}
/* #endif */
