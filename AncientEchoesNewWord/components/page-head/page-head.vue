<template>
  <view class="common-page-head">
    <view class="common-page-head-title-box">
      <text class="common-page-head-title">{{title}}</text>
    </view>
  </view>
</template>
<script lang="uts">
  export default {
    name: "page-head",
    props: {
      title: {
        type: String,
        default: ""
      }
    }
  }
</script>
<style>
  .common-page-head {
    padding: 20px;
    align-items: center;
  }

  .common-page-head-title-box {
    padding: 0 20px;
    border-bottom: 1px solid #D8D8D8;
  }

  .common-page-head-title {
    line-height: 40px;
    color: #BEBEBE;
  }
</style>
