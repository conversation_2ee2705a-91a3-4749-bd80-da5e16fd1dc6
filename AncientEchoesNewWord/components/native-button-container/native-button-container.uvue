<template>
	<view>
		<button type="primary" @tap="testCallMethod">调用组件方法</button>
		<native-button id="helloView1" class="native-button" text="自定义组件"></native-button>
	</view>
</template>

<script>
	import { createNativeButtonContext } from "@/uni_modules/native-button";

	export default {
		data() {
			return {
				title: '组件能力封装示例',
			}
		},

		methods: {
			ontap(e : UniNativeViewEvent) {
				console.log("ontap----------" + e.type)
			},

			testCallMethod: function () {
				let context = createNativeButtonContext("helloView1", this)
				context?.updateText("test code")
			}


		}
	}
</script>

<style>

</style>