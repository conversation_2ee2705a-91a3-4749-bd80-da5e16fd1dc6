<template>
  <view class="uni-navbar">
    <!-- #ifdef APP || WEB -->
    <view v-if="statusBar" class="status-bar"></view>
    <!-- #endif -->
    <!-- #ifdef MP -->
    <view v-if="statusBar" class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    <!-- #endif -->
    <view class="uni-navbar-inner">
      <view class="left-content" @click="back">
        <!-- #ifdef MP-WEIXIN -->
        <text :style="{ color: textColor }" class="uni-icons back-icon"></text>
        <!-- #endif -->
        <!-- #ifndef MP-WEIXIN -->
        <text :style="{ color: textColor }" class="uni-icons">{{
          unicode
        }}</text>
        <!-- #endif -->
      </view>
      <view class="uni-navbar-content" :class="{ 'is-left': isLeft }">
        <text :style="{ color: textColor }">
          <slot>{{ title }}</slot>
        </text>
      </view>
      <view class="right-content">
        <slot name="right"></slot>
      </view>
    </view>
  </view>
</template>

<script>
  // #ifndef MP-WEIXIN
  import iconpath from "./uniicons.ttf";
  // #endif
  export default {
    name: "uni-navbar",
    props: {
      title: {
        type: String,
        default: "",
      },
      isLeft: {
        type: Boolean,
        default: false,
      },
      textColor: {
        type: String,
        default: "#000",
      },
      statusBar: {
        type: Boolean,
        default: false,
      },
      stat: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        statusBarHeight: 0,
      };
    },
    computed: {
      unicode() : string {
        return "\ue600";
      },
    },
    created() {
      // #ifndef MP-WEIXIN
      uni.loadFontFace({
        global: false,
        family: "UniIconsFontFamily",
        source: `url("${iconpath}")`,
        success() { },
        fail(err) {
          console.log(err);
        },
      });
      // #endif

      if (this.stat && this.title != '') {
      	uni.report({
          name:'title',
          options: this.title
        })
      }

    },
    mounted() {
      uni.setNavigationBarColor({
        frontColor: "#000000",
        backgroundColor: "#ffffff",
      });
      // #ifdef MP-WEIXIN
      // TODO 部分小程序平台不支持getWindowInfo
      this.statusBarHeight = uni.getWindowInfo().statusBarHeight
      // #endif
    },
    methods: {
      back() {
        uni.navigateBack({});
      },
    },
  };
</script>

<style>
  /* #ifdef MP-WEIXIN */
  @font-face {
  	font-family: UniIconsFontFamily;
  	src: url('./uniicons.ttf');
  }
  .back-icon:before {
    content: "\e600";
  }
  /* #endif */
  .uni-icons {
    font-family: "UniIconsFontFamily" !important;
    font-size: 26px;
    font-style: normal;
    color: #333;
  }

  .status-bar {
    height: var(--status-bar-height);
  }
  .uni-navbar {
    background-color: #007aff;
  }

  .uni-navbar-inner {
    position: relative;
    flex-direction: row;
    justify-content: space-between;
    height: 45px;
  }

  .left-content,
  .right-content {
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 100%;
  }

  .uni-navbar-content {
    position: absolute;
    height: 100%;
    top: 0;
    bottom: 0;
    left: 45px;
    right: 45px;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }

  .is-left {
    justify-content: flex-start;
  }
</style>
