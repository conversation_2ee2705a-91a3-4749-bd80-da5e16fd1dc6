// 本文件为自动构建生成
import {
  WebViewStyles as WebViewStylesOrigin,
  WebViewProgressStyles as WebViewProgressStylesOrigin,
  ViewToTempFilePathSuccess as ViewToTempFilePathSuccessOrigin,
  ViewToTempFilePathFail as ViewToTempFilePathFailOrigin,
  ViewToTempFilePathOptions as ViewToTempFilePathOptionsOrigin,
  UniWebViewServiceMessageEvent as UniWebViewServiceMessageEventOrigin,
  WebViewServiceMessageEvent as WebViewServiceMessageEventOrigin,
  UniWebViewMessageEventDetail as UniWebViewMessageEventDetailOrigin,
  UniWebViewMessageEvent as UniWebViewMessageEventOrigin,
  WebViewMessageEventDetail as WebViewMessageEventDetailOrigin,
  WebViewMessageEvent as WebViewMessageEventOrigin,
  UniWebViewLoadingEventDetail as UniWebViewLoadingEventDetailOrigin,
  UniWebViewLoadingEvent as UniWebViewLoadingEventOrigin,
  WebViewLoadingEventDetail as WebViewLoadingEventDetailOrigin,
  WebViewLoadingEvent as WebViewLoadingEventOrigin,
  UniWebViewLoadEventDetail as UniWebViewLoadEventDetailOrigin,
  UniWebViewLoadEvent as UniWebViewLoadEventOrigin,
  WebViewLoadedEventDetail as WebViewLoadedEventDetailOrigin,
  WebViewLoadedEvent as WebViewLoadedEventOrigin,
  UniWebViewErrorEventDetail as UniWebViewErrorEventDetailOrigin,
  UniWebViewErrorEvent as UniWebViewErrorEventOrigin,
  WebViewErrorEventDetail as WebViewErrorEventDetailOrigin,
  WebViewErrorEvent as WebViewErrorEventOrigin,
  UniWebViewElementImpl as UniWebViewElementImplOrigin,
  UniWebViewDownloadEventDetail as UniWebViewDownloadEventDetailOrigin,
  UniWebViewDownloadEvent as UniWebViewDownloadEventOrigin,
  WebViewDownloadEventDetail as WebViewDownloadEventDetailOrigin,
  WebViewDownloadEvent as WebViewDownloadEventOrigin,
  UniWebViewContentHeightChangeEventDetail as UniWebViewContentHeightChangeEventDetailOrigin,
  UniWebViewContentHeightChangeEvent as UniWebViewContentHeightChangeEventOrigin,
  UniVideoElement as UniVideoElementOrigin,
  Danmu as DanmuOrigin,
  RequestFullScreenOptions as RequestFullScreenOptionsOrigin,
  UniTouch as UniTouchOrigin,
  UniTouchEvent as UniTouchEventOrigin,
  Touch as TouchOrigin,
  TouchEvent as TouchEventOrigin,
  UniTextareaLineChangeEventDetail as UniTextareaLineChangeEventDetailOrigin,
  UniTextareaLineChangeEvent as UniTextareaLineChangeEventOrigin,
  TextareaLineChangeEvent as TextareaLineChangeEventOrigin,
  TextareaLineChangeEventDetail as TextareaLineChangeEventDetailOrigin,
  UniTextareaFocusEventDetail as UniTextareaFocusEventDetailOrigin,
  UniTextareaFocusEvent as UniTextareaFocusEventOrigin,
  TextareaFocusEvent as TextareaFocusEventOrigin,
  TextareaFocusEventDetail as TextareaFocusEventDetailOrigin,
  UniTextareaBlurEventDetail as UniTextareaBlurEventDetailOrigin,
  UniTextareaBlurEvent as UniTextareaBlurEventOrigin,
  TextareaBlurEvent as TextareaBlurEventOrigin,
  TextareaBlurEventDetail as TextareaBlurEventDetailOrigin,
  UniTextElementImpl as UniTextElementImplOrigin,
  UniTabsElementImpl as UniTabsElementImplOrigin,
  UniTabTapEvent as UniTabTapEventOrigin,
  TabTapEvent as TabTapEventOrigin,
  UniSwiperTransitionEventDetail as UniSwiperTransitionEventDetailOrigin,
  UniSwiperTransitionEvent as UniSwiperTransitionEventOrigin,
  SwiperTransitionEvent as SwiperTransitionEventOrigin,
  UniSwiperChangeEventDetail as UniSwiperChangeEventDetailOrigin,
  UniSwiperChangeEvent as UniSwiperChangeEventOrigin,
  SwiperChangeEvent as SwiperChangeEventOrigin,
  UniSwiperAnimationFinishEventDetail as UniSwiperAnimationFinishEventDetailOrigin,
  UniSwiperAnimationFinishEvent as UniSwiperAnimationFinishEventOrigin,
  SwiperAnimationFinishEvent as SwiperAnimationFinishEventOrigin,
  UniStopNestedScrollEvent as UniStopNestedScrollEventOrigin,
  StopNestedScrollEvent as StopNestedScrollEventOrigin,
  UniStartNestedScrollEvent as UniStartNestedScrollEventOrigin,
  StartNestedScrollEvent as StartNestedScrollEventOrigin,
  UniScrollToUpperEventDetail as UniScrollToUpperEventDetailOrigin,
  UniScrollToUpperEvent as UniScrollToUpperEventOrigin,
  ScrollToUpperEvent as ScrollToUpperEventOrigin,
  UniScrollToLowerEventDetail as UniScrollToLowerEventDetailOrigin,
  UniScrollToLowerEvent as UniScrollToLowerEventOrigin,
  ScrollToLowerEvent as ScrollToLowerEventOrigin,
  UniScrollEventDetail as UniScrollEventDetailOrigin,
  UniScrollEvent as UniScrollEventOrigin,
  ScrollEvent as ScrollEventOrigin,
  UniSafeAreaInsets as UniSafeAreaInsetsOrigin,
  UniRichTextItemClickEventDetail as UniRichTextItemClickEventDetailOrigin,
  UniRichTextItemClickEvent as UniRichTextItemClickEventOrigin,
  RichTextItemClickEventDetail as RichTextItemClickEventDetailOrigin,
  RichTextItemClickEvent as RichTextItemClickEventOrigin,
  UniResizeObserver as UniResizeObserverOrigin,
  UniResizeObserverEntry as UniResizeObserverEntryOrigin,
  UniBorderBoxSize as UniBorderBoxSizeOrigin,
  UniContentBoxSize as UniContentBoxSizeOrigin,
  UniDevicePixelContentBoxSize as UniDevicePixelContentBoxSizeOrigin,
  UniResizeEvent as UniResizeEventOrigin,
  ResizeEvent as ResizeEventOrigin,
  UniRefresherEventDetail as UniRefresherEventDetailOrigin,
  UniRefresherEvent as UniRefresherEventOrigin,
  RefresherEvent as RefresherEventOrigin,
  UniProvider as UniProviderOrigin,
  UniPointerEvent as UniPointerEventOrigin,
  PointerEvent as PointerEventOrigin,
  MouseEvent as MouseEventOrigin,
  UniMouseEvent as UniMouseEventOrigin,
  UniPageScrollEvent as UniPageScrollEventOrigin,
  PageScrollEvent as PageScrollEventOrigin,
  UniPageManager as UniPageManagerOrigin,
  IPageManager as IPageManagerOrigin,
  UniPageEvent as UniPageEventOrigin,
  PageEvent as PageEventOrigin,
  UniPageBody as UniPageBodyOrigin,
  UniPage as UniPageOrigin,
  ExitFullscreenSuccessCallback as ExitFullscreenSuccessCallbackOrigin,
  ExitFullscreenFailCallback as ExitFullscreenFailCallbackOrigin,
  ExitFullscreenCompleteCallback as ExitFullscreenCompleteCallbackOrigin,
  ExitFullscreenOptions as ExitFullscreenOptionsOrigin,
  UniNormalPage as UniNormalPageOrigin,
  UniDialogPage as UniDialogPageOrigin,
  UniNestedPreScrollEvent as UniNestedPreScrollEventOrigin,
  NestedPreScrollEvent as NestedPreScrollEventOrigin,
  UniNativePage as UniNativePageOrigin,
  IPage as IPageOrigin,
  UniNativeApp as UniNativeAppOrigin,
  IApp as IAppOrigin,
  UniInputKeyboardHeightChangeEventDetail as UniInputKeyboardHeightChangeEventDetailOrigin,
  UniInputKeyboardHeightChangeEvent as UniInputKeyboardHeightChangeEventOrigin,
  InputKeyboardHeightChangeEvent as InputKeyboardHeightChangeEventOrigin,
  InputKeyboardHeightChangeEventDetail as InputKeyboardHeightChangeEventDetailOrigin,
  UniInputFocusEventDetail as UniInputFocusEventDetailOrigin,
  UniInputFocusEvent as UniInputFocusEventOrigin,
  InputFocusEvent as InputFocusEventOrigin,
  InputFocusEventDetail as InputFocusEventDetailOrigin,
  UniInputEventDetail as UniInputEventDetailOrigin,
  UniInputEvent as UniInputEventOrigin,
  InputEvent as InputEventOrigin,
  InputEventDetail as InputEventDetailOrigin,
  UniInputConfirmEventDetail as UniInputConfirmEventDetailOrigin,
  UniInputConfirmEvent as UniInputConfirmEventOrigin,
  InputConfirmEvent as InputConfirmEventOrigin,
  InputConfirmEventDetail as InputConfirmEventDetailOrigin,
  UniInputBlurEventDetail as UniInputBlurEventDetailOrigin,
  UniInputBlurEvent as UniInputBlurEventOrigin,
  InputBlurEvent as InputBlurEventOrigin,
  InputBlurEventDetail as InputBlurEventDetailOrigin,
  UniImageLoadEventDetail as UniImageLoadEventDetailOrigin,
  UniImageLoadEvent as UniImageLoadEventOrigin,
  ImageLoadEventDetail as ImageLoadEventDetailOrigin,
  ImageLoadEvent as ImageLoadEventOrigin,
  UniImageErrorEventDetail as UniImageErrorEventDetailOrigin,
  UniImageErrorEvent as UniImageErrorEventOrigin,
  ImageErrorEventDetail as ImageErrorEventDetailOrigin,
  ImageErrorEvent as ImageErrorEventOrigin,
  UniFormControlElement as UniFormControlElementOrigin,
  UniFormControl as UniFormControlOrigin,
  UniEvent as UniEventOrigin,
  UniKeyEvent as UniKeyEventOrigin,
  Event as EventOrigin,
  KeyEvent as KeyEventOrigin,
  UniAppEvent as UniAppEventOrigin,
  UniThemeChangeEvent as UniThemeChangeEventOrigin,
  UniAppErrorEvent as UniAppErrorEventOrigin,
  UniError as UniErrorOrigin,
  UniElementImpl as UniElementImplOrigin,
  UniViewElementImpl as UniViewElementImplOrigin,
  UniDocument as UniDocumentOrigin,
  IDocument as IDocumentOrigin,
  UniCustomEventOptions as UniCustomEventOptionsOrigin,
  UniCustomEvent as UniCustomEventOrigin,
  CustomEventOptions as CustomEventOptionsOrigin,
  CustomEvent as CustomEventOrigin,
  UniCustomElement as UniCustomElementOrigin,
  IUniCustom as IUniCustomOrigin,
  UniCanvasElementImpl as UniCanvasElementImplOrigin,
  UniCallbackWrapper as UniCallbackWrapperOrigin,
  UniAnimationPlaybackEvent as UniAnimationPlaybackEventOrigin,
  UniAnimation as UniAnimationOrigin,
  UniAggregateError as UniAggregateErrorOrigin,
  UTSAndroidHookProxy as UTSAndroidHookProxyOrigin,
  SourceError as SourceErrorOrigin,
  TakeSnapshotOptions as TakeSnapshotOptionsOrigin,
  TakeSnapshotSuccess as TakeSnapshotSuccessOrigin,
  TakeSnapshotFail as TakeSnapshotFailOrigin,
  TakeSnapshotSuccessCallback as TakeSnapshotSuccessCallbackOrigin,
  TakeSnapshotFailCallback as TakeSnapshotFailCallbackOrigin,
  TakeSnapshotCompleteCallback as TakeSnapshotCompleteCallbackOrigin,
  Path2D as Path2DOrigin,
  PageNode as PageNodeOrigin,
  NodeData as NodeDataOrigin,
  INodeData as INodeDataOrigin,
  NativeLoadFontFaceFail as NativeLoadFontFaceFailOrigin,
  NativeLoadFontFaceOptions as NativeLoadFontFaceOptionsOrigin,
  UniNativeViewElement as UniNativeViewElementOrigin,
  UniNativeViewEvent as UniNativeViewEventOrigin,
  UniNativeViewInitEventDetail as UniNativeViewInitEventDetailOrigin,
  UniNativeViewInitEvent as UniNativeViewInitEventOrigin,
  IUniForm as IUniFormOrigin,
  IUniError as IUniErrorOrigin,
  UniElement as UniElementOrigin,
  UniButtonElement as UniButtonElementOrigin,
  UniCanvasElement as UniCanvasElementOrigin,
  UniCommentElement as UniCommentElementOrigin,
  UniImageElement as UniImageElementOrigin,
  Image as ImageOrigin,
  UniInputElement as UniInputElementOrigin,
  UniListItemElement as UniListItemElementOrigin,
  UniListViewElement as UniListViewElementOrigin,
  UniNestedScrollBodyElement as UniNestedScrollBodyElementOrigin,
  UniNestedScrollHeaderElement as UniNestedScrollHeaderElementOrigin,
  UniRichTextElement as UniRichTextElementOrigin,
  UniScrollViewElement as UniScrollViewElementOrigin,
  UniStickyHeaderElement as UniStickyHeaderElementOrigin,
  UniStickySectionElement as UniStickySectionElementOrigin,
  UniSwiperElement as UniSwiperElementOrigin,
  UniSwiperItemElement as UniSwiperItemElementOrigin,
  UniTabsElement as UniTabsElementOrigin,
  UniTextElement as UniTextElementOrigin,
  UniTextareaElement as UniTextareaElementOrigin,
  UniViewElement as UniViewElementOrigin,
  UniWebViewElement as UniWebViewElementOrigin,
  UniWaterFlowElement as UniWaterFlowElementOrigin,
  UniFlowItemElement as UniFlowItemElementOrigin,
  UniShareElement as UniShareElementOrigin,
  GetBoundingClientRectAsyncSuccessCallback as GetBoundingClientRectAsyncSuccessCallbackOrigin,
  GetBoundingClientRectAsyncFailCallback as GetBoundingClientRectAsyncFailCallbackOrigin,
  GetBoundingClientRectAsyncCompleteCallback as GetBoundingClientRectAsyncCompleteCallbackOrigin,
  GetBoundingClientRectAsyncOptions as GetBoundingClientRectAsyncOptionsOrigin,
  RequestFullscreenOptions as RequestFullscreenOptionsOrigin,
  FullscreenErrorCode as FullscreenErrorCodeOrigin,
  IFullscreenError as IFullscreenErrorOrigin,
  FullscreenError as FullscreenErrorOrigin,
  RequestFullscreenSuccessCallback as RequestFullscreenSuccessCallbackOrigin,
  RequestFullscreenFailCallback as RequestFullscreenFailCallbackOrigin,
  RequestFullscreenCompleteCallback as RequestFullscreenCompleteCallbackOrigin,
  UniAnimationKeyframe as UniAnimationKeyframeOrigin,
  UniAnimationOptionDirection as UniAnimationOptionDirectionOrigin,
  UniAnimationOptionEasing as UniAnimationOptionEasingOrigin,
  UniAnimationOptionFill as UniAnimationOptionFillOrigin,
  UniAnimationOption as UniAnimationOptionOrigin,
  INode as INodeOrigin,
  Element as ElementOrigin,
  ITabsNode as ITabsNodeOrigin,
  TextElement as TextElementOrigin,
  UniWebViewElementLoadDataOptions as UniWebViewElementLoadDataOptionsOrigin,
  IWebViewNode as IWebViewNodeOrigin,
  IComment as ICommentOrigin,
  INavigationBar as INavigationBarOrigin,
  DrawableContext as DrawableContextOrigin,
  DOMRect as DOMRectOrigin,
  TextMetrics as TextMetricsOrigin,
  CanvasDirection as CanvasDirectionOrigin,
  CanvasLineCap as CanvasLineCapOrigin,
  CanvasFontStretch as CanvasFontStretchOrigin,
  CanvasGlobalCompositeOperation as CanvasGlobalCompositeOperationOrigin,
  CanvasSmoothingQuality as CanvasSmoothingQualityOrigin,
  CanvasLineJoin as CanvasLineJoinOrigin,
  CanvasTextAlign as CanvasTextAlignOrigin,
  CanvasTextBaseline as CanvasTextBaselineOrigin,
  CanvasTextRendering as CanvasTextRenderingOrigin,
  ImageData as ImageDataOrigin,
  CanvasPattern as CanvasPatternOrigin,
  CanvasGradient as CanvasGradientOrigin,
  CanvasRenderingContext2D as CanvasRenderingContext2DOrigin,
  CSSStyleDeclaration as CSSStyleDeclarationOrigin,
  AsyncApiResult as AsyncApiResultOrigin,
  AsyncApiSuccessResult as AsyncApiSuccessResultOrigin,
} from './index'

declare global {
  const WebViewStyles: typeof WebViewStylesOrigin
  type WebViewStyles = WebViewStylesOrigin
  const WebViewProgressStyles: typeof WebViewProgressStylesOrigin
  type WebViewProgressStyles = WebViewProgressStylesOrigin
  const ViewToTempFilePathSuccess: typeof ViewToTempFilePathSuccessOrigin
  type ViewToTempFilePathSuccess = ViewToTempFilePathSuccessOrigin
  const ViewToTempFilePathFail: typeof ViewToTempFilePathFailOrigin
  type ViewToTempFilePathFail = ViewToTempFilePathFailOrigin
  const ViewToTempFilePathOptions: typeof ViewToTempFilePathOptionsOrigin
  type ViewToTempFilePathOptions = ViewToTempFilePathOptionsOrigin
  const UniWebViewServiceMessageEvent: typeof UniWebViewServiceMessageEventOrigin
  type UniWebViewServiceMessageEvent = UniWebViewServiceMessageEventOrigin
  const WebViewServiceMessageEvent: typeof WebViewServiceMessageEventOrigin
  type WebViewServiceMessageEvent = WebViewServiceMessageEventOrigin
  const UniWebViewMessageEventDetail: typeof UniWebViewMessageEventDetailOrigin
  type UniWebViewMessageEventDetail = UniWebViewMessageEventDetailOrigin
  const UniWebViewMessageEvent: typeof UniWebViewMessageEventOrigin
  type UniWebViewMessageEvent = UniWebViewMessageEventOrigin
  const WebViewMessageEventDetail: typeof WebViewMessageEventDetailOrigin
  const WebViewMessageEvent: typeof WebViewMessageEventOrigin
  type WebViewMessageEventDetail = WebViewMessageEventDetailOrigin
  type WebViewMessageEvent = WebViewMessageEventOrigin
  const UniWebViewLoadingEventDetail: typeof UniWebViewLoadingEventDetailOrigin
  type UniWebViewLoadingEventDetail = UniWebViewLoadingEventDetailOrigin
  const UniWebViewLoadingEvent: typeof UniWebViewLoadingEventOrigin
  type UniWebViewLoadingEvent = UniWebViewLoadingEventOrigin
  const WebViewLoadingEventDetail: typeof WebViewLoadingEventDetailOrigin
  const WebViewLoadingEvent: typeof WebViewLoadingEventOrigin
  type WebViewLoadingEventDetail = WebViewLoadingEventDetailOrigin
  type WebViewLoadingEvent = WebViewLoadingEventOrigin
  const UniWebViewLoadEventDetail: typeof UniWebViewLoadEventDetailOrigin
  type UniWebViewLoadEventDetail = UniWebViewLoadEventDetailOrigin
  const UniWebViewLoadEvent: typeof UniWebViewLoadEventOrigin
  type UniWebViewLoadEvent = UniWebViewLoadEventOrigin
  const WebViewLoadedEventDetail: typeof WebViewLoadedEventDetailOrigin
  const WebViewLoadedEvent: typeof WebViewLoadedEventOrigin
  type WebViewLoadedEventDetail = WebViewLoadedEventDetailOrigin
  type WebViewLoadedEvent = WebViewLoadedEventOrigin
  const UniWebViewErrorEventDetail: typeof UniWebViewErrorEventDetailOrigin
  type UniWebViewErrorEventDetail = UniWebViewErrorEventDetailOrigin
  const UniWebViewErrorEvent: typeof UniWebViewErrorEventOrigin
  type UniWebViewErrorEvent = UniWebViewErrorEventOrigin
  const WebViewErrorEventDetail: typeof WebViewErrorEventDetailOrigin
  const WebViewErrorEvent: typeof WebViewErrorEventOrigin
  type WebViewErrorEventDetail = WebViewErrorEventDetailOrigin
  type WebViewErrorEvent = WebViewErrorEventOrigin
  const UniWebViewElementImpl: typeof UniWebViewElementImplOrigin
  type UniWebViewElementImpl = UniWebViewElementImplOrigin
  const UniWebViewDownloadEventDetail: typeof UniWebViewDownloadEventDetailOrigin
  type UniWebViewDownloadEventDetail = UniWebViewDownloadEventDetailOrigin
  const UniWebViewDownloadEvent: typeof UniWebViewDownloadEventOrigin
  type UniWebViewDownloadEvent = UniWebViewDownloadEventOrigin
  const WebViewDownloadEventDetail: typeof WebViewDownloadEventDetailOrigin
  const WebViewDownloadEvent: typeof WebViewDownloadEventOrigin
  type WebViewDownloadEventDetail = WebViewDownloadEventDetailOrigin
  type WebViewDownloadEvent = WebViewDownloadEventOrigin
  const UniWebViewContentHeightChangeEventDetail: typeof UniWebViewContentHeightChangeEventDetailOrigin
  type UniWebViewContentHeightChangeEventDetail = UniWebViewContentHeightChangeEventDetailOrigin
  const UniWebViewContentHeightChangeEvent: typeof UniWebViewContentHeightChangeEventOrigin
  type UniWebViewContentHeightChangeEvent = UniWebViewContentHeightChangeEventOrigin
  const UniVideoElement: typeof UniVideoElementOrigin
  type UniVideoElement = UniVideoElementOrigin
  type Danmu = DanmuOrigin
  type RequestFullScreenOptions = RequestFullScreenOptionsOrigin
  const UniTouch: typeof UniTouchOrigin
  type UniTouch = UniTouchOrigin
  const UniTouchEvent: typeof UniTouchEventOrigin
  type UniTouchEvent = UniTouchEventOrigin
  const Touch: typeof TouchOrigin
  const TouchEvent: typeof TouchEventOrigin
  type Touch = TouchOrigin
  type TouchEvent = TouchEventOrigin
  const UniTextareaLineChangeEventDetail: typeof UniTextareaLineChangeEventDetailOrigin
  type UniTextareaLineChangeEventDetail = UniTextareaLineChangeEventDetailOrigin
  const UniTextareaLineChangeEvent: typeof UniTextareaLineChangeEventOrigin
  type UniTextareaLineChangeEvent = UniTextareaLineChangeEventOrigin
  const TextareaLineChangeEvent: typeof TextareaLineChangeEventOrigin
  const TextareaLineChangeEventDetail: typeof TextareaLineChangeEventDetailOrigin
  type TextareaLineChangeEvent = TextareaLineChangeEventOrigin
  type TextareaLineChangeEventDetail = TextareaLineChangeEventDetailOrigin
  const UniTextareaFocusEventDetail: typeof UniTextareaFocusEventDetailOrigin
  type UniTextareaFocusEventDetail = UniTextareaFocusEventDetailOrigin
  const UniTextareaFocusEvent: typeof UniTextareaFocusEventOrigin
  type UniTextareaFocusEvent = UniTextareaFocusEventOrigin
  const TextareaFocusEvent: typeof TextareaFocusEventOrigin
  const TextareaFocusEventDetail: typeof TextareaFocusEventDetailOrigin
  type TextareaFocusEvent = TextareaFocusEventOrigin
  type TextareaFocusEventDetail = TextareaFocusEventDetailOrigin
  const UniTextareaBlurEventDetail: typeof UniTextareaBlurEventDetailOrigin
  type UniTextareaBlurEventDetail = UniTextareaBlurEventDetailOrigin
  const UniTextareaBlurEvent: typeof UniTextareaBlurEventOrigin
  type UniTextareaBlurEvent = UniTextareaBlurEventOrigin
  const TextareaBlurEvent: typeof TextareaBlurEventOrigin
  const TextareaBlurEventDetail: typeof TextareaBlurEventDetailOrigin
  type TextareaBlurEvent = TextareaBlurEventOrigin
  type TextareaBlurEventDetail = TextareaBlurEventDetailOrigin
  const UniTextElementImpl: typeof UniTextElementImplOrigin
  type UniTextElementImpl = UniTextElementImplOrigin
  const UniTabsElementImpl: typeof UniTabsElementImplOrigin
  type UniTabsElementImpl = UniTabsElementImplOrigin
  const UniTabTapEvent: typeof UniTabTapEventOrigin
  type UniTabTapEvent = UniTabTapEventOrigin
  const TabTapEvent: typeof TabTapEventOrigin
  type TabTapEvent = TabTapEventOrigin
  const UniSwiperTransitionEventDetail: typeof UniSwiperTransitionEventDetailOrigin
  type UniSwiperTransitionEventDetail = UniSwiperTransitionEventDetailOrigin
  const UniSwiperTransitionEvent: typeof UniSwiperTransitionEventOrigin
  type UniSwiperTransitionEvent = UniSwiperTransitionEventOrigin
  const SwiperTransitionEvent: typeof SwiperTransitionEventOrigin
  type SwiperTransitionEvent = SwiperTransitionEventOrigin
  const UniSwiperChangeEventDetail: typeof UniSwiperChangeEventDetailOrigin
  type UniSwiperChangeEventDetail = UniSwiperChangeEventDetailOrigin
  const UniSwiperChangeEvent: typeof UniSwiperChangeEventOrigin
  type UniSwiperChangeEvent = UniSwiperChangeEventOrigin
  const SwiperChangeEvent: typeof SwiperChangeEventOrigin
  type SwiperChangeEvent = SwiperChangeEventOrigin
  const UniSwiperAnimationFinishEventDetail: typeof UniSwiperAnimationFinishEventDetailOrigin
  type UniSwiperAnimationFinishEventDetail = UniSwiperAnimationFinishEventDetailOrigin
  const UniSwiperAnimationFinishEvent: typeof UniSwiperAnimationFinishEventOrigin
  type UniSwiperAnimationFinishEvent = UniSwiperAnimationFinishEventOrigin
  const SwiperAnimationFinishEvent: typeof SwiperAnimationFinishEventOrigin
  type SwiperAnimationFinishEvent = SwiperAnimationFinishEventOrigin
  const UniStopNestedScrollEvent: typeof UniStopNestedScrollEventOrigin
  type UniStopNestedScrollEvent = UniStopNestedScrollEventOrigin
  const StopNestedScrollEvent: typeof StopNestedScrollEventOrigin
  type StopNestedScrollEvent = StopNestedScrollEventOrigin
  const UniStartNestedScrollEvent: typeof UniStartNestedScrollEventOrigin
  type UniStartNestedScrollEvent = UniStartNestedScrollEventOrigin
  const StartNestedScrollEvent: typeof StartNestedScrollEventOrigin
  type StartNestedScrollEvent = StartNestedScrollEventOrigin
  const UniScrollToUpperEventDetail: typeof UniScrollToUpperEventDetailOrigin
  type UniScrollToUpperEventDetail = UniScrollToUpperEventDetailOrigin
  const UniScrollToUpperEvent: typeof UniScrollToUpperEventOrigin
  type UniScrollToUpperEvent = UniScrollToUpperEventOrigin
  const ScrollToUpperEvent: typeof ScrollToUpperEventOrigin
  type ScrollToUpperEvent = ScrollToUpperEventOrigin
  const UniScrollToLowerEventDetail: typeof UniScrollToLowerEventDetailOrigin
  type UniScrollToLowerEventDetail = UniScrollToLowerEventDetailOrigin
  const UniScrollToLowerEvent: typeof UniScrollToLowerEventOrigin
  type UniScrollToLowerEvent = UniScrollToLowerEventOrigin
  const ScrollToLowerEvent: typeof ScrollToLowerEventOrigin
  type ScrollToLowerEvent = ScrollToLowerEventOrigin
  const UniScrollEventDetail: typeof UniScrollEventDetailOrigin
  type UniScrollEventDetail = UniScrollEventDetailOrigin
  const UniScrollEvent: typeof UniScrollEventOrigin
  type UniScrollEvent = UniScrollEventOrigin
  const ScrollEvent: typeof ScrollEventOrigin
  type ScrollEvent = ScrollEventOrigin
  type UniSafeAreaInsets = UniSafeAreaInsetsOrigin
  const UniRichTextItemClickEventDetail: typeof UniRichTextItemClickEventDetailOrigin
  type UniRichTextItemClickEventDetail = UniRichTextItemClickEventDetailOrigin
  const UniRichTextItemClickEvent: typeof UniRichTextItemClickEventOrigin
  type UniRichTextItemClickEvent = UniRichTextItemClickEventOrigin
  const RichTextItemClickEventDetail: typeof RichTextItemClickEventDetailOrigin
  const RichTextItemClickEvent: typeof RichTextItemClickEventOrigin
  type RichTextItemClickEventDetail = RichTextItemClickEventDetailOrigin
  type RichTextItemClickEvent = RichTextItemClickEventOrigin
  const UniResizeObserver: typeof UniResizeObserverOrigin
  type UniResizeObserver = UniResizeObserverOrigin
  type UniResizeObserverEntry = UniResizeObserverEntryOrigin
  type UniBorderBoxSize = UniBorderBoxSizeOrigin
  type UniContentBoxSize = UniContentBoxSizeOrigin
  type UniDevicePixelContentBoxSize = UniDevicePixelContentBoxSizeOrigin
  const UniResizeEvent: typeof UniResizeEventOrigin
  type UniResizeEvent = UniResizeEventOrigin
  const ResizeEvent: typeof ResizeEventOrigin
  type ResizeEvent = ResizeEventOrigin
  const UniRefresherEventDetail: typeof UniRefresherEventDetailOrigin
  type UniRefresherEventDetail = UniRefresherEventDetailOrigin
  const UniRefresherEvent: typeof UniRefresherEventOrigin
  type UniRefresherEvent = UniRefresherEventOrigin
  const RefresherEvent: typeof RefresherEventOrigin
  type RefresherEvent = RefresherEventOrigin
  type UniProvider = UniProviderOrigin
  const UniPointerEvent: typeof UniPointerEventOrigin
  type UniPointerEvent = UniPointerEventOrigin
  const PointerEvent: typeof PointerEventOrigin
  const MouseEvent: typeof MouseEventOrigin
  const UniMouseEvent: typeof UniMouseEventOrigin
  type PointerEvent = PointerEventOrigin
  type MouseEvent = MouseEventOrigin
  type UniMouseEvent = UniMouseEventOrigin
  const UniPageScrollEvent: typeof UniPageScrollEventOrigin
  type UniPageScrollEvent = UniPageScrollEventOrigin
  const PageScrollEvent: typeof PageScrollEventOrigin
  type PageScrollEvent = PageScrollEventOrigin
  type UniPageManager = UniPageManagerOrigin
  type IPageManager = IPageManagerOrigin
  const UniPageEvent: typeof UniPageEventOrigin
  type UniPageEvent = UniPageEventOrigin
  const PageEvent: typeof PageEventOrigin
  type PageEvent = PageEventOrigin
  type UniPageBody = UniPageBodyOrigin
  type UniPage = UniPageOrigin
  type ExitFullscreenSuccessCallback = ExitFullscreenSuccessCallbackOrigin
  type ExitFullscreenFailCallback = ExitFullscreenFailCallbackOrigin
  type ExitFullscreenCompleteCallback = ExitFullscreenCompleteCallbackOrigin
  type ExitFullscreenOptions = ExitFullscreenOptionsOrigin
  type UniNormalPage = UniNormalPageOrigin
  type UniDialogPage = UniDialogPageOrigin
  const UniNestedPreScrollEvent: typeof UniNestedPreScrollEventOrigin
  type UniNestedPreScrollEvent = UniNestedPreScrollEventOrigin
  const NestedPreScrollEvent: typeof NestedPreScrollEventOrigin
  type NestedPreScrollEvent = NestedPreScrollEventOrigin
  type UniNativePage = UniNativePageOrigin
  type IPage = IPageOrigin
  type UniNativeApp = UniNativeAppOrigin
  type IApp = IAppOrigin
  const UniInputKeyboardHeightChangeEventDetail: typeof UniInputKeyboardHeightChangeEventDetailOrigin
  type UniInputKeyboardHeightChangeEventDetail = UniInputKeyboardHeightChangeEventDetailOrigin
  const UniInputKeyboardHeightChangeEvent: typeof UniInputKeyboardHeightChangeEventOrigin
  type UniInputKeyboardHeightChangeEvent = UniInputKeyboardHeightChangeEventOrigin
  const InputKeyboardHeightChangeEvent: typeof InputKeyboardHeightChangeEventOrigin
  const InputKeyboardHeightChangeEventDetail: typeof InputKeyboardHeightChangeEventDetailOrigin
  type InputKeyboardHeightChangeEvent = InputKeyboardHeightChangeEventOrigin
  type InputKeyboardHeightChangeEventDetail = InputKeyboardHeightChangeEventDetailOrigin
  const UniInputFocusEventDetail: typeof UniInputFocusEventDetailOrigin
  type UniInputFocusEventDetail = UniInputFocusEventDetailOrigin
  const UniInputFocusEvent: typeof UniInputFocusEventOrigin
  type UniInputFocusEvent = UniInputFocusEventOrigin
  const InputFocusEvent: typeof InputFocusEventOrigin
  const InputFocusEventDetail: typeof InputFocusEventDetailOrigin
  type InputFocusEvent = InputFocusEventOrigin
  type InputFocusEventDetail = InputFocusEventDetailOrigin
  const UniInputEventDetail: typeof UniInputEventDetailOrigin
  type UniInputEventDetail = UniInputEventDetailOrigin
  const UniInputEvent: typeof UniInputEventOrigin
  type UniInputEvent = UniInputEventOrigin
  const InputEvent: typeof InputEventOrigin
  const InputEventDetail: typeof InputEventDetailOrigin
  type InputEvent = InputEventOrigin
  type InputEventDetail = InputEventDetailOrigin
  const UniInputConfirmEventDetail: typeof UniInputConfirmEventDetailOrigin
  type UniInputConfirmEventDetail = UniInputConfirmEventDetailOrigin
  const UniInputConfirmEvent: typeof UniInputConfirmEventOrigin
  type UniInputConfirmEvent = UniInputConfirmEventOrigin
  const InputConfirmEvent: typeof InputConfirmEventOrigin
  const InputConfirmEventDetail: typeof InputConfirmEventDetailOrigin
  type InputConfirmEvent = InputConfirmEventOrigin
  type InputConfirmEventDetail = InputConfirmEventDetailOrigin
  const UniInputBlurEventDetail: typeof UniInputBlurEventDetailOrigin
  type UniInputBlurEventDetail = UniInputBlurEventDetailOrigin
  const UniInputBlurEvent: typeof UniInputBlurEventOrigin
  type UniInputBlurEvent = UniInputBlurEventOrigin
  const InputBlurEvent: typeof InputBlurEventOrigin
  const InputBlurEventDetail: typeof InputBlurEventDetailOrigin
  type InputBlurEvent = InputBlurEventOrigin
  type InputBlurEventDetail = InputBlurEventDetailOrigin
  const UniImageLoadEventDetail: typeof UniImageLoadEventDetailOrigin
  type UniImageLoadEventDetail = UniImageLoadEventDetailOrigin
  const UniImageLoadEvent: typeof UniImageLoadEventOrigin
  type UniImageLoadEvent = UniImageLoadEventOrigin
  const ImageLoadEventDetail: typeof ImageLoadEventDetailOrigin
  const ImageLoadEvent: typeof ImageLoadEventOrigin
  type ImageLoadEventDetail = ImageLoadEventDetailOrigin
  type ImageLoadEvent = ImageLoadEventOrigin
  const UniImageErrorEventDetail: typeof UniImageErrorEventDetailOrigin
  type UniImageErrorEventDetail = UniImageErrorEventDetailOrigin
  const UniImageErrorEvent: typeof UniImageErrorEventOrigin
  type UniImageErrorEvent = UniImageErrorEventOrigin
  const ImageErrorEventDetail: typeof ImageErrorEventDetailOrigin
  const ImageErrorEvent: typeof ImageErrorEventOrigin
  type ImageErrorEventDetail = ImageErrorEventDetailOrigin
  type ImageErrorEvent = ImageErrorEventOrigin
  const UniFormControlElement: typeof UniFormControlElementOrigin
  type UniFormControlElement<T> = UniFormControlElementOrigin<T>
  type UniFormControl<T> = UniFormControlOrigin<T>
  const UniEvent: typeof UniEventOrigin
  type UniEvent = UniEventOrigin
  const UniKeyEvent: typeof UniKeyEventOrigin
  type UniKeyEvent = UniKeyEventOrigin
  const Event: typeof EventOrigin
  const KeyEvent: typeof KeyEventOrigin
  const UniAppEvent: typeof UniAppEventOrigin
  type UniAppEvent = UniAppEventOrigin
  const UniThemeChangeEvent: typeof UniThemeChangeEventOrigin
  type UniThemeChangeEvent = UniThemeChangeEventOrigin
  const UniAppErrorEvent: typeof UniAppErrorEventOrigin
  type UniAppErrorEvent = UniAppErrorEventOrigin
  type Event = EventOrigin
  type KeyEvent = KeyEventOrigin
  const UniError: typeof UniErrorOrigin
  type UniError = UniErrorOrigin
  const UniElementImpl: typeof UniElementImplOrigin
  type UniElementImpl = UniElementImplOrigin
  const UniViewElementImpl: typeof UniViewElementImplOrigin
  type UniViewElementImpl = UniViewElementImplOrigin
  type UniDocument = UniDocumentOrigin
  type IDocument = IDocumentOrigin
  const UniCustomEventOptions: typeof UniCustomEventOptionsOrigin
  type UniCustomEventOptions<T> = UniCustomEventOptionsOrigin<T>
  const UniCustomEvent: typeof UniCustomEventOrigin
  type UniCustomEvent<T> = UniCustomEventOrigin<T>
  const CustomEventOptions: typeof CustomEventOptionsOrigin
  const CustomEvent: typeof CustomEventOrigin
  type CustomEventOptions<T> = CustomEventOptionsOrigin<T>
  type CustomEvent<T> = CustomEventOrigin<T>
  type UniCustomElement = UniCustomElementOrigin
  type IUniCustom = IUniCustomOrigin
  const UniCanvasElementImpl: typeof UniCanvasElementImplOrigin
  type UniCanvasElementImpl = UniCanvasElementImplOrigin
  const UniCallbackWrapper: typeof UniCallbackWrapperOrigin
  type UniCallbackWrapper = UniCallbackWrapperOrigin
  const UniAnimationPlaybackEvent: typeof UniAnimationPlaybackEventOrigin
  type UniAnimationPlaybackEvent = UniAnimationPlaybackEventOrigin
  const UniAnimation: typeof UniAnimationOrigin
  type UniAnimation = UniAnimationOrigin
  const UniAggregateError: typeof UniAggregateErrorOrigin
  type UniAggregateError = UniAggregateErrorOrigin
  type UTSAndroidHookProxy = UTSAndroidHookProxyOrigin
  const SourceError: typeof SourceErrorOrigin
  type SourceError = SourceErrorOrigin
  const TakeSnapshotOptions: typeof TakeSnapshotOptionsOrigin
  type TakeSnapshotOptions = TakeSnapshotOptionsOrigin
  type TakeSnapshotSuccess = TakeSnapshotSuccessOrigin
  type TakeSnapshotFail = TakeSnapshotFailOrigin
  type TakeSnapshotSuccessCallback = TakeSnapshotSuccessCallbackOrigin
  type TakeSnapshotFailCallback = TakeSnapshotFailCallbackOrigin
  type TakeSnapshotCompleteCallback = TakeSnapshotCompleteCallbackOrigin
  const Path2D: typeof Path2DOrigin
  type Path2D = Path2DOrigin
  const PageNode: typeof PageNodeOrigin
  type PageNode = PageNodeOrigin
  const NodeData: typeof NodeDataOrigin
  type NodeData = NodeDataOrigin
  type INodeData = INodeDataOrigin
  const NativeLoadFontFaceFail: typeof NativeLoadFontFaceFailOrigin
  type NativeLoadFontFaceFail = NativeLoadFontFaceFailOrigin
  const NativeLoadFontFaceOptions: typeof NativeLoadFontFaceOptionsOrigin
  type NativeLoadFontFaceOptions = NativeLoadFontFaceOptionsOrigin
  const UniNativeViewElement: typeof UniNativeViewElementOrigin
  const UniNativeViewEvent: typeof UniNativeViewEventOrigin
  type UniNativeViewEvent = UniNativeViewEventOrigin
  const UniNativeViewInitEventDetail: typeof UniNativeViewInitEventDetailOrigin
  type UniNativeViewInitEventDetail = UniNativeViewInitEventDetailOrigin
  const UniNativeViewInitEvent: typeof UniNativeViewInitEventOrigin
  type UniNativeViewInitEvent = UniNativeViewInitEventOrigin
  type UniNativeViewElement = UniNativeViewElementOrigin
  type IUniForm = IUniFormOrigin
  type IUniError = IUniErrorOrigin
  const UniElement: typeof UniElementOrigin
  const UniButtonElement: typeof UniButtonElementOrigin
  const UniCanvasElement: typeof UniCanvasElementOrigin
  const UniCommentElement: typeof UniCommentElementOrigin
  const UniImageElement: typeof UniImageElementOrigin
  const Image: typeof ImageOrigin
  const UniInputElement: typeof UniInputElementOrigin
  const UniListItemElement: typeof UniListItemElementOrigin
  const UniListViewElement: typeof UniListViewElementOrigin
  const UniNestedScrollBodyElement: typeof UniNestedScrollBodyElementOrigin
  const UniNestedScrollHeaderElement: typeof UniNestedScrollHeaderElementOrigin
  const UniRichTextElement: typeof UniRichTextElementOrigin
  const UniScrollViewElement: typeof UniScrollViewElementOrigin
  const UniStickyHeaderElement: typeof UniStickyHeaderElementOrigin
  const UniStickySectionElement: typeof UniStickySectionElementOrigin
  const UniSwiperElement: typeof UniSwiperElementOrigin
  const UniSwiperItemElement: typeof UniSwiperItemElementOrigin
  const UniTabsElement: typeof UniTabsElementOrigin
  const UniTextElement: typeof UniTextElementOrigin
  const UniTextareaElement: typeof UniTextareaElementOrigin
  const UniViewElement: typeof UniViewElementOrigin
  const UniWebViewElement: typeof UniWebViewElementOrigin
  const UniWaterFlowElement: typeof UniWaterFlowElementOrigin
  const UniFlowItemElement: typeof UniFlowItemElementOrigin
  const UniShareElement: typeof UniShareElementOrigin
  type GetBoundingClientRectAsyncSuccessCallback = GetBoundingClientRectAsyncSuccessCallbackOrigin
  type GetBoundingClientRectAsyncFailCallback = GetBoundingClientRectAsyncFailCallbackOrigin
  type GetBoundingClientRectAsyncCompleteCallback = GetBoundingClientRectAsyncCompleteCallbackOrigin
  type GetBoundingClientRectAsyncOptions = GetBoundingClientRectAsyncOptionsOrigin
  type UniElement = UniElementOrigin
  type RequestFullscreenOptions = RequestFullscreenOptionsOrigin
  type FullscreenErrorCode = FullscreenErrorCodeOrigin
  type IFullscreenError = IFullscreenErrorOrigin
  type FullscreenError = FullscreenErrorOrigin
  type RequestFullscreenSuccessCallback = RequestFullscreenSuccessCallbackOrigin
  type RequestFullscreenFailCallback = RequestFullscreenFailCallbackOrigin
  type RequestFullscreenCompleteCallback = RequestFullscreenCompleteCallbackOrigin
  type UniAnimationKeyframe = UniAnimationKeyframeOrigin
  type UniAnimationOptionDirection = UniAnimationOptionDirectionOrigin
  type UniAnimationOptionEasing = UniAnimationOptionEasingOrigin
  type UniAnimationOptionFill = UniAnimationOptionFillOrigin
  type UniAnimationOption = UniAnimationOptionOrigin
  type INode = INodeOrigin
  type Element = ElementOrigin
  type UniViewElement = UniViewElementOrigin
  type UniScrollViewElement = UniScrollViewElementOrigin
  type UniListViewElement = UniListViewElementOrigin
  type UniListItemElement = UniListItemElementOrigin
  type UniSwiperElement = UniSwiperElementOrigin
  type UniSwiperItemElement = UniSwiperItemElementOrigin
  type UniImageElement = UniImageElementOrigin
  type Image = ImageOrigin
  type UniInputElement = UniInputElementOrigin
  type UniTextareaElement = UniTextareaElementOrigin
  type UniRichTextElement = UniRichTextElementOrigin
  type UniStickyHeaderElement = UniStickyHeaderElementOrigin
  type UniStickySectionElement = UniStickySectionElementOrigin
  type UniTabsElement = UniTabsElementOrigin
  type ITabsNode = ITabsNodeOrigin
  type UniTextElement = UniTextElementOrigin
  type TextElement = TextElementOrigin
  type UniWebViewElementLoadDataOptions = UniWebViewElementLoadDataOptionsOrigin
  type UniWebViewElement = UniWebViewElementOrigin
  type IWebViewNode = IWebViewNodeOrigin
  type UniCommentElement = UniCommentElementOrigin
  type IComment = ICommentOrigin
  type UniButtonElement = UniButtonElementOrigin
  type UniNestedScrollHeaderElement = UniNestedScrollHeaderElementOrigin
  type UniNestedScrollBodyElement = UniNestedScrollBodyElementOrigin
  type UniCanvasElement = UniCanvasElementOrigin
  type UniWaterFlowElement = UniWaterFlowElementOrigin
  type UniFlowItemElement = UniFlowItemElementOrigin
  type UniShareElement = UniShareElementOrigin
  type INavigationBar = INavigationBarOrigin
  const DrawableContext: typeof DrawableContextOrigin
  type DrawableContext = DrawableContextOrigin
  const DOMRect: typeof DOMRectOrigin
  type DOMRect = DOMRectOrigin
  type TextMetrics = TextMetricsOrigin
  type CanvasDirection = CanvasDirectionOrigin
  type CanvasLineCap = CanvasLineCapOrigin
  type CanvasFontStretch = CanvasFontStretchOrigin
  type CanvasGlobalCompositeOperation = CanvasGlobalCompositeOperationOrigin
  type CanvasSmoothingQuality = CanvasSmoothingQualityOrigin
  type CanvasLineJoin = CanvasLineJoinOrigin
  type CanvasTextAlign = CanvasTextAlignOrigin
  type CanvasTextBaseline = CanvasTextBaselineOrigin
  type CanvasTextRendering = CanvasTextRenderingOrigin
  type ImageData = ImageDataOrigin
  type CanvasPattern = CanvasPatternOrigin
  type CanvasGradient = CanvasGradientOrigin
  type CanvasRenderingContext2D = CanvasRenderingContext2DOrigin
  const CSSStyleDeclaration: typeof CSSStyleDeclarationOrigin
  type CSSStyleDeclaration = CSSStyleDeclarationOrigin
  type AsyncApiResult = AsyncApiResultOrigin
  type AsyncApiSuccessResult = AsyncApiSuccessResultOrigin
}
