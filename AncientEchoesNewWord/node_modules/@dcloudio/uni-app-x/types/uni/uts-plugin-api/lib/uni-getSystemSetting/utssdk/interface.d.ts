

export type GetSystemSettingResult = {
  /**
   * 蓝牙是否开启
   *
   * @uniPlatform
   * {
   * 	"app": {
   * 		"android": {
   * 			"osVer": "5.0",
   * 			"uniVer": "√",
   * 			"unixVer": "3.9+"
   * 		},
   * 		"ios": {
   * 			"osVer": "12.0",
   * 			"uniVer": "√",
   * 			"unixVer": "4.11"
   * 		},
  *    "harmony": {
  *      "osVer": "3.0",
  *      "uniVer": "4.25",
  *      "unixVer": "4.61"
  *    }
   * 	},
   *  "mp": {
   *    "weixin": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "4.41"
   *    },
   *    "alipay": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "baidu": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "toutiao": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "lark": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "qq": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "kuaishou": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "jd": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    }
   *  }
   * }
   */
  bluetoothEnabled?: boolean,
  /**
   * 蓝牙的报错信息
   *
   * @uniPlatform
   * {
   * 	"app": {
   * 		"android": {
   * 			"osVer": "5.0",
   * 			"uniVer": "√",
   * 			"unixVer": "3.9+"
   * 		},
   * 		"ios": {
   * 			"osVer": "12.0",
   * 			"uniVer": "√",
   * 			"unixVer": "4.11"
   * 		},
  *    "harmony": {
  *      "osVer": "3.0",
  *      "uniVer": "4.25",
  *      "unixVer": "4.61"
  *    }
   * 	},
   *  "mp": {
   *    "weixin": {
   *        "hostVer": "√",
   *        "uniVer": "x",
   *        "unixVer": "4.41"
   *    },
   *    "alipay": {
   *        "hostVer": "√",
   *        "uniVer": "x",
   *        "unixVer": "x"
   *    },
   *    "baidu": {
   *        "hostVer": "√",
   *        "uniVer": "x",
   *        "unixVer": "x"
   *    },
   *    "toutiao": {
   *        "hostVer": "√",
   *        "uniVer": "x",
   *        "unixVer": "x"
   *    },
   *    "lark": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "qq": {
   *        "hostVer": "√",
   *        "uniVer": "x",
   *        "unixVer": "x"
   *    },
   *    "kuaishou": {
   *        "hostVer": "√",
   *        "uniVer": "x",
   *        "unixVer": "x"
   *    },
   *    "jd": {
   *        "hostVer": "√",
   *        "uniVer": "x",
   *        "unixVer": "x"
   *    }
   *  }
   * }
   */
  bluetoothError?: string | null,
  /**
   * 位置是否开启
   *
   * @uniPlatform
   * {
   * 	"app": {
   * 		"android": {
   * 			"osVer": "5.0",
   * 			"uniVer": "√",
   * 			"unixVer": "3.9+"
   * 		},
   * 		"ios": {
   * 			"osVer": "12.0",
   * 			"uniVer": "√",
   * 			"unixVer": "4.11"
   * 		},
  *    "harmony": {
  *      "osVer": "3.0",
  *      "uniVer": "4.25",
  *      "unixVer": "4.61"
  *    }
   * 	},
   *  "mp": {
   *    "weixin": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "4.41"
   *    },
   *    "alipay": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "baidu": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "toutiao": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "lark": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "qq": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "kuaishou": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "jd": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    }
   *  }
   * }
   */
  locationEnabled: boolean,
  /**
   * wifi是否开启
   *
   * @uniPlatform
   * {
   * 	"app": {
   * 		"android": {
   * 			"osVer": "5.0",
   * 			"uniVer": "√",
   * 			"unixVer": "3.9+"
   * 		},
   * 		"ios": {
   * 			"osVer": "12.0",
   * 			"uniVer": "√",
   * 			"unixVer": "4.11"
   * 		},
  *    "harmony": {
  *      "osVer": "3.0",
  *      "uniVer": "4.25",
  *      "unixVer": "4.61"
  *    }
   * 	},
   *  "mp": {
   *    "weixin": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "4.41"
   *    },
   *    "alipay": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "baidu": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "toutiao": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "lark": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "qq": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "kuaishou": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "jd": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    }
   *  }
   * }
   */
  wifiEnabled?: boolean,
  /**
   * wifi的报错信息
   *
   * @uniPlatform
   * {
   * 	"app": {
   * 		"android": {
   * 			"osVer": "5.0",
   * 			"uniVer": "√",
   * 			"unixVer": "3.9+"
   * 		},
   * 		"ios": {
   * 			"osVer": "x",
   * 			"uniVer": "x",
   * 			"unixVer": "x"
   * 		},
  *    "harmony": {
  *      "osVer": "3.0",
  *      "uniVer": "4.25",
  *      "unixVer": "4.61"
  *    }
   * 	},
   *  "mp": {
   *    "weixin": {
   *        "hostVer": "√",
   *        "uniVer": "x",
   *        "unixVer": "4.41"
   *    },
   *    "alipay": {
   *        "hostVer": "√",
   *        "uniVer": "x",
   *        "unixVer": "x"
   *    },
   *    "baidu": {
   *        "hostVer": "√",
   *        "uniVer": "x",
   *        "unixVer": "x"
   *    },
   *    "toutiao": {
   *        "hostVer": "√",
   *        "uniVer": "x",
   *        "unixVer": "x"
   *    },
   *    "lark": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "qq": {
   *        "hostVer": "√",
   *        "uniVer": "x",
   *        "unixVer": "x"
   *    },
   *    "kuaishou": {
   *        "hostVer": "√",
   *        "uniVer": "x",
   *        "unixVer": "x"
   *    },
   *    "jd": {
   *        "hostVer": "√",
   *        "uniVer": "x",
   *        "unixVer": "x"
   *    }
   *  }
   * }
   */
  wifiError?: string | null,
  /**
   * 设备方向
   *
   * @uniPlatform
   * {
   * 	"app": {
   * 		"android": {
   * 			"osVer": "5.0",
   * 			"uniVer": "√",
   * 			"unixVer": "3.9+"
   * 		},
   * 		"ios": {
   * 			"osVer": "12.0",
   * 			"uniVer": "√",
   * 			"unixVer": "4.11"
   * 		},
  *    "harmony": {
  *      "osVer": "3.0",
  *      "uniVer": "4.25",
  *      "unixVer": "4.61"
  *    }
   * 	},
   *  "mp": {
   *    "weixin": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "4.41"
   *    },
   *    "alipay": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "baidu": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "toutiao": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "lark": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "qq": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "kuaishou": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    },
   *    "jd": {
   *        "hostVer": "√",
   *        "uniVer": "√",
   *        "unixVer": "x"
   *    }
   *  }
   * }
   */
  deviceOrientation:
  /**
   * 纵向
   */
  'portrait' |
  /**
   * 横向
   */
  'landscape',
}


export type GetSystemSetting = () => GetSystemSettingResult


export interface Uni {
  /**
    * GetSystemSetting()
    * @description
    * 获取系统设置
    * @return {object}
    * @tutorial https://doc.dcloud.net.cn/uni-app-x/api/get-system-setting.html
    * @tutorial_uni_app_x https://doc.dcloud.net.cn/uni-app-x/api/get-system-setting.html
    * @tutorial_uni_app https://uniapp.dcloud.net.cn/api/system/getsystemsetting.html
    * @uniPlatform {
    *   "app": {
    *     "android": {
    *       "osVer": "5.0",
    *       "uniVer": "√",
    *       "unixVer": "3.9+"
    *     },
    *     "ios": {
    *       "osVer": "12.0",
    *       "uniVer": "√",
    *       "unixVer": "4.11"
    *     },
    *     "harmony": {
    *       "osVer": "3.0",
    *       "uniVer": "4.31",
    *       "unixVer": "4.61"
    *     }
    *   },
    *   "mp": {
    *     "weixin": {
    *       "hostVer": "2.20.1",
    *       "uniVer": "√",
    *       "unixVer": "4.41"
    *     },
    *     "alipay": {
    *       "hostVer": "√",
    *       "uniVer": "√",
    *       "unixVer": "x"
    *     },
    *     "baidu": {
    *       "hostVer": "√",
    *       "uniVer": "√",
    *       "unixVer": "x"
    *     },
    *     "toutiao": {
    *       "hostVer": "√",
    *       "uniVer": "√",
    *       "unixVer": "x"
    *     },
    *     "lark": {
    *       "hostVer": "√",
    *       "uniVer": "√",
    *       "unixVer": "x"
    *     },
    *     "qq": {
    *       "hostVer": "√",
    *       "uniVer": "√",
    *       "unixVer": "x"
    *     },
    *     "kuaishou": {
    *       "hostVer": "√",
    *       "uniVer": "√",
    *       "unixVer": "x"
    *     },
    *     "jd": {
    *       "hostVer": "√",
    *       "uniVer": "√",
    *       "unixVer": "x"
    *     }
    *   },
    *   "web": {
    *     "uniVer": "x",
    *     "unixVer": "x"
    *   }
    * }
    * @example
     ```typescript
      uni.getSystemSetting()
     ```
     * @tutorial_weixin https://developers.weixin.qq.com/miniprogram/dev/api/base/system/wx.getSystemSetting.html
     */
  getSystemSetting(): GetSystemSettingResult;
}
