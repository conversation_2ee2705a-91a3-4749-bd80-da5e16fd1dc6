// 本文件为自动构建生成
import {
  ReadFileSuccessResult as ReadFileSuccessResultOrigin,
  OpenFileSuccessResult as OpenFileSuccessResultOrigin,
  FileManagerSuccessResult as FileManagerSuccessResultOrigin,
  FileManagerSuccessCallback as FileManagerSuccessCallbackOrigin,
  FileManagerFailCallback as FileManagerFailCallbackOrigin,
  FileManagerCompleteCallback as FileManagerCompleteCallbackOrigin,
  ReadFileSuccessCallback as ReadFileSuccessCallbackOrigin,
  ReadFileOptions as ReadFileOptionsOrigin,
  WriteFileOptions as WriteFileOptionsOrigin,
  AppendFileOptions as AppendFileOptionsOrigin,
  OpenFileSuccessCallback as OpenFileSuccessCallbackOrigin,
  OpenFileOptions as OpenFileOptionsOrigin,
  OpenFileSyncOptions as OpenFileSyncOptionsOrigin,
  UnLinkSuccessCallback as UnLinkSuccessCallbackOrigin,
  UnLinkOptions as UnLinkOptions<PERSON>rigin,
  MkDirSuccessCallback as MkDirSuccessCallbackOrigin,
  MkDirOptions as MkDirOptionsOrigin,
  RmDirOptions as RmDirOptionsOrigin,
  ReadDirSuccessResult as ReadDirSuccessResultOrigin,
  ReadDirSuccessCallback as ReadDirSuccessCallbackOrigin,
  ReadDirOptions as ReadDirOptionsOrigin,
  AccessOptions as AccessOptionsOrigin,
  RenameOptions as RenameOptionsOrigin,
  CopyFileOptions as CopyFileOptionsOrigin,
  SaveFileOptions as SaveFileOptionsOrigin,
  SaveFileSuccessCallback as SaveFileSuccessCallbackOrigin,
  SaveFileSuccessResult as SaveFileSuccessResultOrigin,
  GetFileInfoSuccessResult as GetFileInfoSuccessResultOrigin,
  GetFileInfoSuccessCallback as GetFileInfoSuccessCallbackOrigin,
  GetFileInfoOptions as GetFileInfoOptionsOrigin,
  Stats as StatsOrigin,
  Stats as StatsOrigin,
  FileStats as FileStatsOrigin,
  StatSuccessResult as StatSuccessResultOrigin,
  StatSuccessCallback as StatSuccessCallbackOrigin,
  StatOptions as StatOptionsOrigin,
  UnzipFileOptions as UnzipFileOptionsOrigin,
  GetSavedFileListResult as GetSavedFileListResultOrigin,
  GetSavedFileListCallback as GetSavedFileListCallbackOrigin,
  GetSavedFileListOptions as GetSavedFileListOptionsOrigin,
  TruncateFileOptions as TruncateFileOptionsOrigin,
  ReadCompressedFileResult as ReadCompressedFileResultOrigin,
  ReadCompressedFileCallback as ReadCompressedFileCallbackOrigin,
  ReadCompressedFileOptions as ReadCompressedFileOptionsOrigin,
  RemoveSavedFileOptions as RemoveSavedFileOptionsOrigin,
  WriteResult as WriteResultOrigin,
  WriteCallback as WriteCallbackOrigin,
  WriteOptions as WriteOptionsOrigin,
  WriteSyncOptions as WriteSyncOptionsOrigin,
  CloseOptions as CloseOptionsOrigin,
  CloseSyncOptions as CloseSyncOptionsOrigin,
  FStatSuccessResult as FStatSuccessResultOrigin,
  FStatSuccessCallback as FStatSuccessCallbackOrigin,
  FStatOptions as FStatOptionsOrigin,
  FStatSyncOptions as FStatSyncOptionsOrigin,
  FTruncateFileOptions as FTruncateFileOptionsOrigin,
  FTruncateFileSyncOptions as FTruncateFileSyncOptionsOrigin,
  EntryItem as EntryItemOrigin,
  EntriesResult as EntriesResultOrigin,
  ZipFileItem as ZipFileItemOrigin,
  ReadZipEntryCallback as ReadZipEntryCallbackOrigin,
  ReadZipEntryOptions as ReadZipEntryOptionsOrigin,
  ReadSuccessCallbackResult as ReadSuccessCallbackResultOrigin,
  ReadSuccessCallback as ReadSuccessCallbackOrigin,
  ReadOption as ReadOptionOrigin,
  ReadSyncOption as ReadSyncOptionOrigin,
  ReadResult as ReadResultOrigin,
  FileSystemManager as FileSystemManagerOrigin,
  GetFileSystemManager as GetFileSystemManagerOrigin,
  FileSystemManagerErrorCode as FileSystemManagerErrorCodeOrigin,
  FileSystemManagerFail as FileSystemManagerFailOrigin,
  IFileSystemManagerFail as IFileSystemManagerFailOrigin,
  Uni as UniOrigin
} from './interface'

declare global {
  type ReadFileSuccessResult = ReadFileSuccessResultOrigin
  type OpenFileSuccessResult = OpenFileSuccessResultOrigin
  type FileManagerSuccessResult = FileManagerSuccessResultOrigin
  type FileManagerSuccessCallback = FileManagerSuccessCallbackOrigin
  type FileManagerFailCallback = FileManagerFailCallbackOrigin
  type FileManagerCompleteCallback = FileManagerCompleteCallbackOrigin
  type ReadFileSuccessCallback = ReadFileSuccessCallbackOrigin
  type ReadFileOptions = ReadFileOptionsOrigin
  type WriteFileOptions = WriteFileOptionsOrigin
  type AppendFileOptions = AppendFileOptionsOrigin
  type OpenFileSuccessCallback = OpenFileSuccessCallbackOrigin
  type OpenFileOptions = OpenFileOptionsOrigin
  type OpenFileSyncOptions = OpenFileSyncOptionsOrigin
  type UnLinkSuccessCallback = UnLinkSuccessCallbackOrigin
  type UnLinkOptions = UnLinkOptionsOrigin
  type MkDirSuccessCallback = MkDirSuccessCallbackOrigin
  type MkDirOptions = MkDirOptionsOrigin
  type RmDirOptions = RmDirOptionsOrigin
  type ReadDirSuccessResult = ReadDirSuccessResultOrigin
  type ReadDirSuccessCallback = ReadDirSuccessCallbackOrigin
  type ReadDirOptions = ReadDirOptionsOrigin
  type AccessOptions = AccessOptionsOrigin
  type RenameOptions = RenameOptionsOrigin
  type CopyFileOptions = CopyFileOptionsOrigin
  type SaveFileOptions = SaveFileOptionsOrigin
  type SaveFileSuccessCallback = SaveFileSuccessCallbackOrigin
  type SaveFileSuccessResult = SaveFileSuccessResultOrigin
  type GetFileInfoSuccessResult = GetFileInfoSuccessResultOrigin
  type GetFileInfoSuccessCallback = GetFileInfoSuccessCallbackOrigin
  type GetFileInfoOptions = GetFileInfoOptionsOrigin
  type Stats = StatsOrigin
  type Stats = StatsOrigin
  type FileStats = FileStatsOrigin
  type StatSuccessResult = StatSuccessResultOrigin
  type StatSuccessCallback = StatSuccessCallbackOrigin
  type StatOptions = StatOptionsOrigin
  type UnzipFileOptions = UnzipFileOptionsOrigin
  type GetSavedFileListResult = GetSavedFileListResultOrigin
  type GetSavedFileListCallback = GetSavedFileListCallbackOrigin
  type GetSavedFileListOptions = GetSavedFileListOptionsOrigin
  type TruncateFileOptions = TruncateFileOptionsOrigin
  type ReadCompressedFileResult = ReadCompressedFileResultOrigin
  type ReadCompressedFileCallback = ReadCompressedFileCallbackOrigin
  type ReadCompressedFileOptions = ReadCompressedFileOptionsOrigin
  type RemoveSavedFileOptions = RemoveSavedFileOptionsOrigin
  type WriteResult = WriteResultOrigin
  type WriteCallback = WriteCallbackOrigin
  type WriteOptions = WriteOptionsOrigin
  type WriteSyncOptions = WriteSyncOptionsOrigin
  type CloseOptions = CloseOptionsOrigin
  type CloseSyncOptions = CloseSyncOptionsOrigin
  type FStatSuccessResult = FStatSuccessResultOrigin
  type FStatSuccessCallback = FStatSuccessCallbackOrigin
  type FStatOptions = FStatOptionsOrigin
  type FStatSyncOptions = FStatSyncOptionsOrigin
  type FTruncateFileOptions = FTruncateFileOptionsOrigin
  type FTruncateFileSyncOptions = FTruncateFileSyncOptionsOrigin
  type EntryItem = EntryItemOrigin
  type EntriesResult = EntriesResultOrigin
  type ZipFileItem = ZipFileItemOrigin
  type ReadZipEntryCallback = ReadZipEntryCallbackOrigin
  type ReadZipEntryOptions = ReadZipEntryOptionsOrigin
  type ReadSuccessCallbackResult = ReadSuccessCallbackResultOrigin
  type ReadSuccessCallback = ReadSuccessCallbackOrigin
  type ReadOption = ReadOptionOrigin
  type ReadSyncOption = ReadSyncOptionOrigin
  type ReadResult = ReadResultOrigin
  type FileSystemManager = FileSystemManagerOrigin
  type GetFileSystemManager = GetFileSystemManagerOrigin
  type FileSystemManagerErrorCode = FileSystemManagerErrorCodeOrigin
  type FileSystemManagerFail = FileSystemManagerFailOrigin
  type IFileSystemManagerFail = IFileSystemManagerFailOrigin
  interface Uni extends UniOrigin { }
}
