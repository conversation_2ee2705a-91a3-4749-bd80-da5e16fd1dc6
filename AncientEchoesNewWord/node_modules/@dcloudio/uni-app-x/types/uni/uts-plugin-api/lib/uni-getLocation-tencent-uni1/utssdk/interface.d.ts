import { GetLocationFailImpl as GetLocationFailImplement } from './unierror.uts'

export type GetLocationFailImpl = GetLocationFailImplement

/**
 * 错误码
 */
export type LocationErrorCode =
	/**
	 * 系统定位未开启，请在系统设置中开启系统定位
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        }
	 *    }
	 * }
	 */
	1505003 |
	/**
	 * 应用定位权限未开启
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        }
	 *    }
	 * }
	 */
	1505004 |
	/**
	 * 超时
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        }
	 *    }
	 * }
	 */
	1505600 |
	/**
	 * 不支持的定位类型
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        }
	 *    }
	 * }
	 */
	1505601 |
	/**
	 * 捕获定位失败
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        }
	 *    }
	 * }
	 */
	1505602 |
	/**
	 * 逆地理编码捕获失败
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        }
	 *    }
	 * }
	 */
	1505603 |
	/**
	 * 服务供应商获取失败
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        }
	 *    }
	 * }
	 */
	1505604 |
	/**
	 * 不支持逆地理编码
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        }
	 *    }
	 * }
	 */
	1505700 |
	/**
	 * 没有找到具体的定位引擎（GPS_PROVIDER，NETWORK_PROVIDER，PASSIVE_PROVIDER等），请确定系统定位是否开启
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        }
	 *    }
	 * }
	 */
	1505701 |
	/**
	 * 应用高精度定位权限未开启
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        }
	 *    }
	 * }
	 */
	1505800 |
	/**
	 * 未通过配置预校验，通常是腾讯定位 api key 配置错误
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        }
	 *    }
	 * }
	 */
	1505605 |
	/**
	 * 腾讯定位只支持GCJ-02
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "4.31",
	 *            "unixVer": "4.25"
	 *        }
	 *    }
	 * }
	 */
	1505607;

/**
 * 网络请求失败的错误回调参数
 */
export interface IGetLocationFail extends IUniError {
	errCode : LocationErrorCode
};

export type GetLocationFail = IGetLocationFail


export type GetLocation = (options : GetLocationOptions) => void;
export type GetLocationSuccess = {
	/**
	 * 纬度，浮点数，范围为-90~90，负数表示南纬
	 * @defaultValue 0
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "√",
	 *            "unixVer": "3.9.0"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "√",
	 *            "unixVer": "4.11"
	 *        },
	 *    "harmony": {
	 *      "osVer": "x",
	 *      "uniVer": "4.25",
	 *      "unixVer": "4.61"
	 *    }
	 *    },
	 *    "web": {
	 *        "uniVer": "√",
	 *        "unixVer": "4.0"
	 *    }
	 * }
	 */
	latitude : number,
	/**
	 * 经度，范围为-180~180，负数表示西经
	 * @defaultValue 0
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "√",
	 *            "unixVer": "3.9.0"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "√",
	 *            "unixVer": "4.11"
	 *        },
	 *    "harmony": {
	 *      "osVer": "x",
	 *      "uniVer": "4.25",
	 *      "unixVer": "4.61"
	 *    }
	 *    },
	 *    "web": {
	 *        "uniVer": "√",
	 *        "unixVer": "4.0"
	 *    }
	 * }
	 */
	longitude : number,
	/**
	 * 速度，浮点数，单位m/s
	 * @defaultValue 0
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "√",
	 *            "unixVer": "3.9.0"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "√",
	 *            "unixVer": "4.11"
	 *        },
	 *    "harmony": {
	 *      "osVer": "x",
	 *      "uniVer": "4.25",
	 *      "unixVer": "4.61"
	 *    }
	 *    },
	 *    "web": {
	 *        "uniVer": "√",
	 *        "unixVer": "4.0"
	 *    }
	 * }
	 */
	speed : number,
	/**
	 * 位置的精确度
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "√",
	 *            "unixVer": "3.9.0"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "√",
	 *            "unixVer": "4.11"
	 *        },
	 *    "harmony": {
	 *      "osVer": "x",
	 *      "uniVer": "4.25",
	 *      "unixVer": "4.61"
	 *    }
	 *    },
	 *    "web": {
	 *        "uniVer": "√",
	 *        "unixVer": "4.0"
	 *    }
	 * }
	 */
	accuracy : number,
	/**
	 * 高度，单位 m
	 * @defaultValue 0
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "√",
	 *            "unixVer": "3.9.0"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "√",
	 *            "unixVer": "4.11"
	 *        },
	 *    "harmony": {
	 *      "osVer": "x",
	 *      "uniVer": "4.25",
	 *      "unixVer": "4.61"
	 *    }
	 *    },
	 *    "web": {
	 *        "uniVer": "√",
	 *        "unixVer": "4.0"
	 *    }
	 * }
	 */
	altitude : number,
	/**
	 * 垂直精度，单位 m（Android 无法获取，返回 0）
	 * @defaultValue 0
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "√",
	 *            "unixVer": "3.9.0"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "√",
	 *            "unixVer": "4.11"
	 *        },
	 *    "harmony": {
	 *      "osVer": "x",
	 *      "uniVer": "x",
	 *      "unixVer": "x"
	 *    }
	 *    },
	 *    "web": {
	 *        "uniVer": "√",
	 *        "unixVer": "4.0"
	 *    }
	 * }
	 */
	verticalAccuracy : number,
	/**
	 * 水平精度，单位 m
	 * @defaultValue 0
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "√",
	 *            "unixVer": "3.9.0"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "√",
	 *            "unixVer": "4.11"
	 *        },
	 *    "harmony": {
	 *      "osVer": "x",
	 *      "uniVer": "x",
	 *      "unixVer": "x"
	 *    }
	 *    },
	 *    "web": {
	 *        "uniVer": "√",
	 *        "unixVer": "4.0"
	 *    }
	 * }
	 */
	horizontalAccuracy : number,
	/**
	 * 地址信息
	 * @defaultValue null
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "√",
	 *            "unixVer": "3.9.0"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "√",
	 *            "unixVer": "4.11"
	 *        },
	 *    "harmony": {
	 *      "osVer": "x",
	 *      "uniVer": "x",
	 *      "unixVer": "x"
	 *    }
	 *    },
	 *    "web": {
	 *        "uniVer": "√",
	 *        "unixVer": "4.0"
	 *    }
	 * }
	 */
	address : string | null
};
type GetLocationSuccessCallback = (result : GetLocationSuccess) => void;
type GetLocationFailCallback = (result : GetLocationFail) => void;
type GetLocationComplete = any;
type GetLocationCompleteCallback = (result : GetLocationComplete) => void;
export type GetLocationOptions = {
	/**
	 * 默认为 wgs84 返回 gps 坐标，gcj02 返回可用于uni.openLocation的坐标，web端需配置定位 SDK 信息才可支持 gcj02；
	 * @defaultValue wgs84
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "√",
	 *            "unixVer": "3.9.0"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "√",
	 *            "unixVer": "4.11"
	 *        },
	 *    "harmony": {
	 *      "osVer": "x",
	 *      "uniVer": "4.25",
	 *      "unixVer": "4.61"
	 *    }
	 *    },
	 *    "web": {
	 *        "uniVer": "√",
	 *        "unixVer": "4.0"
	 *    }
	 * }
	 */
	type ?:
	/**
	 * wgs84坐标系，系统定位默认取值wgs84，系统定位仅支持wgs84坐标系
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "3.9.0"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.11"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "√",
	 *    "unixVer": "4.0"
	 *  }
	 * }
	 */
	"wgs84" |
	/**
	 * gcj02坐标系，腾讯定位默认取值gcj02，腾讯定位仅支持gcj02坐标系
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "3.9.0"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.11"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "√",
	 *    "unixVer": "4.0"
	 *  }
	 * }
	 */
	"gcj02" | null,
	/**
	 * 传入 true 会返回高度信息，由于获取高度需要较高精确度，会减慢接口返回速度
	 * @type boolean
	 * @defaultValue false
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "√",
	 *            "unixVer": "3.9.0"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "√",
	 *            "unixVer": "4.11"
	 *        },
	 *    "harmony": {
	 *      "osVer": "x",
	 *      "uniVer": "4.25",
	 *      "unixVer": "4.61"
	 *    }
	 *    },
	 *    "web": {
	 *        "uniVer": "√",
	 *        "unixVer": "4.0"
	 *    }
	 * }
	 */
	altitude ?: boolean | null,
	/**
	 * 传入 true 会解析地址；
	 * 使用系统定位时平台差异：iOS支持逆地理编码，Android不支持；
	 * @type boolean
	 * @defaultValue false
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "√",
	 *            "unixVer": "3.9.0"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "√",
	 *            "unixVer": "4.11"
	 *        },
	 *    "harmony": {
	 *      "osVer": "x",
	 *      "uniVer": "x",
	 *      "unixVer": "x"
	 *    }
	 *    },
	 *    "web": {
	 *        "uniVer": "x",
	 *        "unixVer": "x"
	 *    }
	 * }
	 */
	geocode ?: boolean | null,
	/**
	 * 高精度定位超时时间(ms)，指定时间内返回最高精度，该值3000ms以上高精度定位才有效果
	 * @defaultValue 3000
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "√",
	 *            "unixVer": "3.9.0"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "√",
	 *            "unixVer": "4.11"
	 *        },
	 *    "harmony": {
	 *      "osVer": "x",
	 *      "uniVer": "4.25",
	 *      "unixVer": "4.61"
	 *    }
	 *    },
	 *    "web": {
	 *        "uniVer": "x",
	 *        "unixVer": "x"
	 *    }
	 * }
	 */
	highAccuracyExpireTime ?: number | null,
	/**
	 * 开启高精度定位
	 * @type boolean
	 * @defaultValue false
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "√",
	 *            "unixVer": "3.9.0"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "√",
	 *            "unixVer": "4.11"
	 *        },
	 *    "harmony": {
	 *      "osVer": "x",
	 *      "uniVer": "4.25",
	 *      "unixVer": "4.61"
	 *    }
	 *    },
	 *    "web": {
	 *        "uniVer": "√",
	 *        "unixVer": "4.0"
	 *    }
	 * }
	 */
	isHighAccuracy ?: boolean | null,
	/**
	 * 接口调用成功的回调函数
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "√",
	 *            "unixVer": "3.9.0"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "√",
	 *            "unixVer": "4.11"
	 *        },
	 *    "harmony": {
	 *      "osVer": "x",
	 *      "uniVer": "4.25",
	 *      "unixVer": "4.61"
	 *    }
	 *    },
	 *    "web": {
	 *        "uniVer": "√",
	 *        "unixVer": "4.0"
	 *    }
	 * }
	 */
	success ?: GetLocationSuccessCallback | null,
	/**
	 * 接口调用失败的回调函数
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "√",
	 *            "unixVer": "3.9.0"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "√",
	 *            "unixVer": "4.11"
	 *        },
	 *    "harmony": {
	 *      "osVer": "x",
	 *      "uniVer": "4.25",
	 *      "unixVer": "4.61"
	 *    }
	 *    },
	 *    "web": {
	 *        "uniVer": "√",
	 *        "unixVer": "4.0"
	 *    }
	 * }
	 */
	fail ?: GetLocationFailCallback | null,
	/**
	 * 接口调用结束的回调函数（调用成功、失败都会执行）
	 * @uniPlatform {
	 *    "app": {
	 *        "android": {
	 *            "osVer": "5.0",
	 *            "uniVer": "√",
	 *            "unixVer": "3.9.0"
	 *        },
	 *        "ios": {
	 *            "osVer": "12.0",
	 *            "uniVer": "√",
	 *            "unixVer": "4.11"
	 *        },
	 *    "harmony": {
	 *      "osVer": "x",
	 *      "uniVer": "4.25",
	 *      "unixVer": "4.61"
	 *    }
	 *    },
	 *    "web": {
	 *        "uniVer": "√",
	 *        "unixVer": "4.0"
	 *    }
	 * }
	 */
	complete ?: GetLocationCompleteCallback | null
};
