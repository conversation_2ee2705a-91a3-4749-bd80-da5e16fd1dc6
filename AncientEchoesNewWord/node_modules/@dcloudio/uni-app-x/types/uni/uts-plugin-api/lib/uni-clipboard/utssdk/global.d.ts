// 本文件为自动构建生成
import {
  ClipBoardErrorCode as ClipBoardErrorCodeOrigin,
  IClipBoardError as IClipBoardErrorOrigin,
  SetClipboardData as SetClipboardDataOrigin,
  SetClipboardDataSuccess as SetClipboardDataSuccessOrigin,
  SetClipboardDataSuccessCallback as SetClipboardDataSuccessCallbackOrigin,
  SetClipboardDataFail as SetClipboardDataFailOrigin,
  SetClipboardDataFailCallback as SetClipboardDataFailCallbackOrigin,
  SetClipboardDataComplete as SetClipboardDataCompleteOrigin,
  SetClipboardDataCompleteCallback as SetClipboardDataCompleteCallbackOrigin,
  SetClipboardDataOptions as SetClipboardDataOptionsOrigin,
  GetClipboardData as GetClipboardDataOrigin,
  GetClipboardDataSuccess as GetClipboardDataSuccessOrigin,
  GetClipboardDataSuccessCallback as GetClipboardDataSuccessCallbackOrigin,
  GetClipboardDataFail as GetClipboardDataFail<PERSON><PERSON>in,
  GetClipboardDataFailCallback as GetClipboardDataFailCallbackOrigin,
  GetClipboardDataComplete as GetClipboardDataCompleteOrigin,
  GetClipboardDataCompleteCallback as GetClipboardDataCompleteCallbackOrigin,
  GetClipboardDataOptions as GetClipboardDataOptionsOrigin,
  Uni as UniOrigin
} from './interface'

declare global {
  type ClipBoardErrorCode = ClipBoardErrorCodeOrigin
  type IClipBoardError = IClipBoardErrorOrigin
  type SetClipboardData = SetClipboardDataOrigin
  type SetClipboardDataSuccess = SetClipboardDataSuccessOrigin
  type SetClipboardDataSuccessCallback = SetClipboardDataSuccessCallbackOrigin
  type SetClipboardDataFail = SetClipboardDataFailOrigin
  type SetClipboardDataFailCallback = SetClipboardDataFailCallbackOrigin
  type SetClipboardDataComplete = SetClipboardDataCompleteOrigin
  type SetClipboardDataCompleteCallback = SetClipboardDataCompleteCallbackOrigin
  type SetClipboardDataOptions = SetClipboardDataOptionsOrigin
  type GetClipboardData = GetClipboardDataOrigin
  type GetClipboardDataSuccess = GetClipboardDataSuccessOrigin
  type GetClipboardDataSuccessCallback = GetClipboardDataSuccessCallbackOrigin
  type GetClipboardDataFail = GetClipboardDataFailOrigin
  type GetClipboardDataFailCallback = GetClipboardDataFailCallbackOrigin
  type GetClipboardDataComplete = GetClipboardDataCompleteOrigin
  type GetClipboardDataCompleteCallback = GetClipboardDataCompleteCallbackOrigin
  type GetClipboardDataOptions = GetClipboardDataOptionsOrigin
  interface Uni extends UniOrigin { }
}
