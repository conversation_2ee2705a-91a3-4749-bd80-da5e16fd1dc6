import { GetProviderFailImpl as GetProviderFailImplement } from './unierror.uts'

export type GetProviderFailImpl = GetProviderFailImplement
export type GetProviderSuccess = {
	/**
	 * 服务类型：支付 (payment)
	 *
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.18"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.18"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	service :
	/**
	 * 支付 (alipay、wxpay)
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.11"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.18"
	 *    },
	 * 		"harmony": {
	 *      "osVer": "3.0",
	 *      "uniVer": "4.25",
	 *      "unixVer": "4.61"
	 * 		}
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	'payment' |
	/**
	 * 授权登录
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "x"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "x"
	 *    },
	 * 		"harmony": {
	 *      "osVer": "3.0",
	 *      "uniVer": "4.27",
	 *      "unixVer": "4.61"
	 * 		}
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	'oauth'|
	/**
	 * 分享
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "x",
	 *      "uniVer": "x",
	 *      "unixVer": "x"
	 *    },
	 *    "ios": {
	 *      "osVer": "x",
	 *      "uniVer": "x",
	 *      "unixVer": "x"
	 *    },
	 * 		"harmony": {
	 *      "osVer": "5.0.0",
	 *      "uniVer": "4.66",
	 *      "unixVer": "4.66"
	 * 		}
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	'share',
	/**
	 * 得到的服务供应商
	 * @type PlusShareShareService['id'][] | PlusPushClientInfo['id'][] | PlusOauthAuthService['id'][] | PlusPaymentPaymentChannel['id'][]
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.18"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.18"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	provider : string[],
	/**
	 * 得到的服务供应商服务对象
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.18"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.18"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	providers : UniProvider[]

};

export type GetProviderSyncSuccess = {
	/**
	 * 服务类型
	 *
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.25"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.25"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	service :
	/**
	 * 支付 (alipay、wxpay)
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "x",
	 *      "unixVer": "4.25"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "x",
	 *      "unixVer": "4.25"
	 *    },
	 *    "harmony": {
	 *      "osVer": "3.0",
	 *      "uniVer": "4.25",
	 *      "unixVer": "4.61"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	'payment' |
	/**
	 * 定位 (system、tencent)
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "x",
	 *      "unixVer": "4.25"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "x",
	 *      "unixVer": "4.25"
	 *    },
	 *    "harmony": {
	 *      "osVer": "3.0",
	 *      "uniVer": "4.25",
	 *      "unixVer": "4.61"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	'location' |
	/**
	 * 授权登录
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "x"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "x"
	 *    },
	 * 		"harmony": {
	 *      "osVer": "3.0",
	 *      "uniVer": "4.27",
	 *      "unixVer": "4.61"
	 * 		}
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	'oauth',
	/**
	 * 得到的服务供应商
	 * @type PlusShareShareService['id'][] | PlusPushClientInfo['id'][] | PlusOauthAuthService['id'][] | PlusPaymentPaymentChannel['id'][]
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "x",
	 *      "unixVer": "4.25"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "x",
	 *      "unixVer": "4.25"
	 *    },
	 *    "harmony": {
	 *      "osVer": "3.0",
	 *      "uniVer": "4.25",
	 *      "unixVer": "4.61"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	providerIds : string[],
	/**
	 * 得到的服务供应商服务对象
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "x",
	 *      "unixVer": "4.25"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "x",
	 *      "unixVer": "4.25"
	 *    },
	 *    "harmony": {
	 *      "osVer": "3.0",
	 *      "uniVer": "4.25",
	 *      "unixVer": "4.61"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	providerObjects : UniProvider[]

};
export type GetProviderSync = (options : GetProviderSyncOptions) => GetProviderSyncSuccess;
export type GetProviderSyncOptions = {
	/**
	 * 服务类型
	 *
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.25"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.25"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	service :
	/**
	 * 支付 (alipay、wxpay)
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.25"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.25"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	'payment' |
	/**
	 * 定位 (system、tencent)
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.25"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.25"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	'location' |
	/**
	 * 授权登录
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "x"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "x"
	 *    },
	 * 		"harmony": {
	 *      "osVer": "3.0",
	 *      "uniVer": "4.27",
	 *      "unixVer": "4.61"
	 * 		}
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	'oauth',
};

export type GetProviderSuccessCallback = (result : GetProviderSuccess) => void;
export type GetProviderFail = IGetProviderFail;
export type GetProviderFailCallback = (result : GetProviderFail) => void;
export type GetProviderComplete = any;
export type GetProviderCompleteCallback = (result : GetProviderComplete) => void;
export type GetProviderOptions = {
	/**
	 * 服务类型：支付 (payment)
	 *
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.18"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.18"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	service :
	/**
	 * 支付 (alipay、wxpay)
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.11"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.18"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	'payment' |
	/**
	 * 授权登录
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "x"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "x"
	 *    },
	 * 		"harmony": {
	 *      "osVer": "3.0",
	 *      "uniVer": "4.27",
	 *      "unixVer": "4.61"
	 * 		}
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	'oauth',
	/**
	 * 接口调用成功的回调
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.18"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.18"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	success ?: GetProviderSuccessCallback | null,
	/**
	 * 接口调用失败的回调函数
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.18"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.18"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	fail ?: GetProviderFailCallback | null,
	/**
	 * 接口调用结束的回调函数（调用成功、失败都会执行）
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.18"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.18"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	*/
	complete ?: GetProviderCompleteCallback | null
};

export type GetProvider = (options : GetProviderOptions) => void;


export interface Uni {
	/**
	 * 获取服务供应商
	 * @deprecated 已废弃，4.25及以后版本请使用getProviderSync()方法代替
	 * @param {GetProviderOptions} options
	 * @return {void}
	 * @tutorial https://doc.dcloud.net.cn/uni-app-x/api/provider.html
	 * @tutorial_uni_app_x https://doc.dcloud.net.cn/uni-app-x/api/provider.html
	 * @tutorial_uni_app https://uniapp.dcloud.net.cn/api/plugins/provider.html#getprovider
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.11"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.18"
	 *    },
	 *    "harmony": {
	 *      "osVer": "3.0",
	 *      "uniVer": "4.23",
	 *      "unixVer": "4.61"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	getProvider(options : GetProviderOptions) : void;
	/**
	 * getProvider的同步方法
	 * @param {GetProviderSyncOptions} options
	 * @tutorial https://doc.dcloud.net.cn/uni-app-x/api/provider.html#getprovidersync
	 * @tutorial_uni_app_x https://doc.dcloud.net.cn/uni-app-x/api/provider.html#getprovidersync
	 * @tutorial_uni_app https://uniapp.dcloud.net.cn/api/plugins/provider.html#getprovider
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "x",
	 *      "unixVer": "4.25"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "x",
	 *      "unixVer": "4.25"
	 *    },
	 *    "harmony": {
	 *      "osVer": "3.0",
	 *      "uniVer": "4.25",
	 *      "unixVer": "4.61"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	getProviderSync(options : GetProviderSyncOptions) : GetProviderSyncSuccess;
}
/**
 * 错误码：
 * 110600：服务类型参数无效。
 */
export type ProviderErrorCode =
	/**
	 * 服务类型参数无效。
	 * @uniPlatform {
	 *  "app": {
	 *    "android": {
	 *      "osVer": "5.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.25"
	 *    },
	 *    "ios": {
	 *      "osVer": "12.0",
	 *      "uniVer": "√",
	 *      "unixVer": "4.25"
	 *    }
	 *  },
	 *  "web": {
	 *    "uniVer": "x",
	 *    "unixVer": "x"
	 *  }
	 * }
	 */
	110600;
export interface IGetProviderFail extends IUniError {
	errCode : ProviderErrorCode
};
