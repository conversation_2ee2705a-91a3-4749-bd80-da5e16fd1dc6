// 本文件为自动构建生成
export {
  ReadFileSuccessResult,
  OpenFileSuccessResult,
  FileManagerSuccessResult,
  FileManagerSuccessCallback,
  FileManagerFailCallback,
  FileManagerCompleteCallback,
  ReadFileSuccessCallback,
  ReadFileOptions,
  WriteFileOptions,
  AppendFileOptions,
  OpenFileSuccessCallback,
  OpenFileOptions,
  OpenFileSyncOptions,
  UnLinkSuccessCallback,
  UnLinkOptions,
  MkDirSuccessCallback,
  MkDirOptions,
  RmDirOptions,
  ReadDirSuccessResult,
  ReadDirSuccessCallback,
  ReadDirOptions,
  AccessOptions,
  RenameOptions,
  CopyFileOptions,
  SaveFileOptions,
  SaveFileSuccessCallback,
  SaveFileSuccessResult,
  GetFileInfoSuccessResult,
  GetFileInfoSuccessCallback,
  GetFileInfoOptions,
  Stats,
  Stats,
  FileStats,
  StatSuccessResult,
  StatSuccessCallback,
  StatOptions,
  UnzipFileOptions,
  GetSavedFileListResult,
  GetSavedFileListCallback,
  GetSavedFileListOptions,
  TruncateFileOptions,
  ReadCompressedFileResult,
  ReadCompressedFileCallback,
  ReadCompressedFileOptions,
  RemoveSavedFileOptions,
  WriteResult,
  WriteCallback,
  WriteOptions,
  WriteSyncOptions,
  CloseOptions,
  CloseSyncOptions,
  FStatSuccessResult,
  FStatSuccessCallback,
  FStatOptions,
  FStatSyncOptions,
  FTruncateFileOptions,
  FTruncateFileSyncOptions,
  EntryItem,
  EntriesResult,
  ZipFileItem,
  ReadZipEntryCallback,
  ReadZipEntryOptions,
  ReadSuccessCallbackResult,
  ReadSuccessCallback,
  ReadOption,
  ReadSyncOption,
  ReadResult,
  FileSystemManager,
  GetFileSystemManager,
  FileSystemManagerErrorCode,
  FileSystemManagerFail,
  IFileSystemManagerFail,
} from './interface'
