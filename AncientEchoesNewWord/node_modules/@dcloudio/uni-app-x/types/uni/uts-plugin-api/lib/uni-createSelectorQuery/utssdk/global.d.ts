// 本文件为自动构建生成
import {
  CreateSelectorQuery as CreateSelector<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  SelectorQueryNodeInfoCallback as SelectorQueryNodeInfoCallbackOrigin,
  NodeInfo as NodeInfoO<PERSON>in,
  NodeField as NodeField<PERSON><PERSON><PERSON>,
  NodesRef as NodesRef<PERSON><PERSON><PERSON>,
  SelectorQuery as SelectorQuery<PERSON><PERSON>in,
  Uni as UniOrigin
} from './interface'

declare global {
  type CreateSelectorQuery = CreateSelectorQueryOrigin
  type SelectorQueryNodeInfoCallback = SelectorQueryNodeInfoCallbackOrigin
  type NodeInfo = NodeInfoOrigin
  type NodeField = NodeFieldOrigin
  type NodesRef = NodesRefOrigin
  type SelectorQuery = SelectorQueryOrigin
  interface Uni extends UniOrigin { }
}
