// 本文件为自动构建生成
import {
  PreviewImageErrorCode as PreviewImageErrorCodeOrigin,
  IPreviewImageError as IPreviewImageErrorOrigin,
  PreviewImageSuccess as PreviewImageSuccessOrigin,
  PreviewImageFail as PreviewImageFailOrigin,
  PreviewImageSuccessCallback as PreviewImageSuccessCallbackOrigin,
  PreviewImageFailCallback as PreviewImageFailCallbackOrigin,
  PreviewImageCompleteCallback as PreviewImageCompleteCallbackOrigin,
  LongPressActionsSuccessResult as LongPressActionsSuccessResultOrigin,
  LongPressActionsFailResult as LongPressActionsFailResultOrigin,
  LongPressActionsOptions as LongPressActionsOptionsOrigin,
  PreviewImageOptions as PreviewImageOptionsOrigin,
  PreviewImage as PreviewImageOrigin,
  ClosePreviewImage as ClosePreviewImageOrigin,
  ClosePreviewImageSuccess as ClosePreviewImageSuccessOrigin,
  ClosePreviewImageFail as ClosePreviewImageFailOrigin,
  ClosePreviewImageSuccessCallback as ClosePreviewImageSuccessCallbackOrigin,
  ClosePreviewImageFailCallback as ClosePreviewImageFailCallbackOrigin,
  ClosePreviewImageCompleteCallback as ClosePreviewImageCompleteCallbackOrigin,
  ClosePreviewImageOptions as ClosePreviewImageOptionsOrigin,
  Uni as UniOrigin
} from './interface'

declare global {
  type PreviewImageErrorCode = PreviewImageErrorCodeOrigin
  type IPreviewImageError = IPreviewImageErrorOrigin
  type PreviewImageSuccess = PreviewImageSuccessOrigin
  type PreviewImageFail = PreviewImageFailOrigin
  type PreviewImageSuccessCallback = PreviewImageSuccessCallbackOrigin
  type PreviewImageFailCallback = PreviewImageFailCallbackOrigin
  type PreviewImageCompleteCallback = PreviewImageCompleteCallbackOrigin
  type LongPressActionsSuccessResult = LongPressActionsSuccessResultOrigin
  type LongPressActionsFailResult = LongPressActionsFailResultOrigin
  type LongPressActionsOptions = LongPressActionsOptionsOrigin
  type PreviewImageOptions = PreviewImageOptionsOrigin
  type PreviewImage = PreviewImageOrigin
  type ClosePreviewImage = ClosePreviewImageOrigin
  type ClosePreviewImageSuccess = ClosePreviewImageSuccessOrigin
  type ClosePreviewImageFail = ClosePreviewImageFailOrigin
  type ClosePreviewImageSuccessCallback = ClosePreviewImageSuccessCallbackOrigin
  type ClosePreviewImageFailCallback = ClosePreviewImageFailCallbackOrigin
  type ClosePreviewImageCompleteCallback = ClosePreviewImageCompleteCallbackOrigin
  type ClosePreviewImageOptions = ClosePreviewImageOptionsOrigin
  interface Uni extends UniOrigin { }
}
