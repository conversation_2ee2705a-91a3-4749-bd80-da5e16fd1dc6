// 本文件为自动构建生成
import {
  GetLocationFailImpl as GetLocationFailImplOrigin,
  LocationErrorCode as LocationErrorCodeOrigin,
  IGetLocationFail as IGetLocationFailOrigin,
  GetLocationFail as GetLocationFailOrigin,
  GetLocation as GetLocation<PERSON><PERSON><PERSON>,
  GetLocationSuc<PERSON> as GetLocationSuccessOrigin,
  GetLocationOptions as GetLocationOptionsOrigin,
} from './interface'

declare global {
  type GetLocationFailImpl = GetLocationFailImplOrigin
  type LocationErrorCode = LocationErrorCodeOrigin
  type IGetLocationFail = IGetLocationFailOrigin
  type GetLocationFail = GetLocationFailOrigin
  type GetLocation = GetLocationOrigin
  type GetLocationSuccess = GetLocationSuccessOrigin
  type GetLocationOptions = GetLocationOptionsOrigin
}
