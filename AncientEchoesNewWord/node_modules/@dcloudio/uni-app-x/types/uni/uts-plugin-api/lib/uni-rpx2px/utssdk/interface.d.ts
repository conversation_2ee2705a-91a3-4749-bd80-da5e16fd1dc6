export type Rpx2px = (number: number) => number;

export interface Uni {
    /**
     * 将rpx单位值转换成px
     * @tutorial_uni_app https://uniapp.dcloud.net.cn/api/ui/font.html#upx2px
     * @tutorial_uni_app_x https://doc.dcloud.net.cn/uni-app-x/api/rpx2px.html
     * @tutorial https://doc.dcloud.net.cn/uni-app-x/api/rpx2px.html
     * @uniPlatform {
     *  "app": {
     *    "android": {
     *      "osVer": "5.0",
     *      "uniVer": "√",
     *      "unixVer": "4.02"
     *    },
     *    "ios": {
     *      "osVer": "12.0",
     *      "uniVer": "√",
     *      "unixVer": "4.11"
     *    },
     *    "harmony": {
     *      "osVer": "3.0",
     *      "uniVer": "4.23",
     *      "unixVer": "4.61"
     *    }
     *  },
      *  "mp": {
      *    "weixin": {
      *        "hostVer": "√",
      *        "uniVer": "√",
      *        "unixVer": "4.41"
      *    },
      *    "alipay": {
      *        "hostVer": "√",
      *        "uniVer": "√",
      *        "unixVer": "x"
      *    },
      *    "baidu": {
      *        "hostVer": "√",
      *        "uniVer": "√",
      *        "unixVer": "x"
      *    },
      *    "toutiao": {
      *        "hostVer": "√",
      *        "uniVer": "√",
      *        "unixVer": "x"
      *    },
      *    "lark": {
      *        "hostVer": "√",
      *        "uniVer": "√",
      *        "unixVer": "x"
      *    },
      *    "qq": {
      *        "hostVer": "√",
      *        "uniVer": "√",
      *        "unixVer": "x"
      *    },
      *    "kuaishou": {
      *        "hostVer": "√",
      *        "uniVer": "√",
      *        "unixVer": "x"
      *    },
      *    "jd": {
      *        "hostVer": "√",
      *        "uniVer": "√",
      *        "unixVer": "x"
      *    }
      *  },
     *  "web": {
     *    "uniVer": "√",
     *    "unixVer": "4.02"
     *  }
     * }
     * @example
      ```typescript
        uni.rpx2px(200)
      ```
     */
    rpx2px(number: number): number;
}
