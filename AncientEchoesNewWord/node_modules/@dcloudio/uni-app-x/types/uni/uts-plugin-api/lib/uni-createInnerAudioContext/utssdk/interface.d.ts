
export interface Uni {
    /**
     * 创建并返回 audio 上下文 audioContext 对象
     *
     * @tutorial https://doc.dcloud.net.cn/uni-app-x/api/create-inner-audio-context.html
     * @tutorial_uni_app_x https://doc.dcloud.net.cn/uni-app-x/api/create-inner-audio-context.html
     * @tutorial_uni_app http://uniapp.dcloud.io/api/media/audio-context.html
     * @uniPlatform {
     *   "app": {
     *     "android": {
     *       "osVer": "5.0",
     *       "uniVer": "√",
     *       "uniUtsPlugin": "x",
     *       "unixVer": "4.33",
     *       "unixUtsPlugin": "4.33"
     *     },
     *     "ios": {
     *       "osVer": "12.0",
     *       "uniVer": "√",
     *       "uniUtsPlugin": "x",
     *       "unixVer": "4.41",
     *       "unixUtsPlugin": "4.41"
     *     },
     *     "harmony": {
     *       "osVer": "5.0.0",
     *       "uniVer": "4.23",
     *       "unixVer": "4.61"
     *     }
     *   },
     *   "mp": {
     *     "weixin": {
     *       "hostVer": "1.6.0",
     *       "uniVer": "√",
     *       "unixVer": "4.41"
     *     },
     *     "alipay": {
     *       "hostVer": "1.23.4",
     *       "uniVer": "√",
     *       "unixVer": "x"
     *     },
     *     "baidu": {
     *       "hostVer": "√",
     *       "uniVer": "√",
     *       "unixVer": "x"
     *     },
     *     "toutiao": {
     *       "hostVer": "√",
     *       "uniVer": "√",
     *       "unixVer": "x"
     *     },
     *     "lark": {
     *       "hostVer": "√",
     *       "uniVer": "√",
     *       "unixVer": "x"
     *     },
     *     "qq": {
     *       "hostVer": "√",
     *       "uniVer": "√",
     *       "unixVer": "x"
     *     },
     *     "kuaishou": {
     *       "hostVer": "√",
     *       "uniVer": "√",
     *       "unixVer": "x"
     *     },
     *     "jd": {
     *       "hostVer": "√",
     *       "uniVer": "√",
     *       "unixVer": "x"
     *     }
     *   },
     *   "web": {
     *     "uniVer": "√",
     *     "unixVer": "4.0"
     *   }
     * }
     * @tutorial_weixin https://developers.weixin.qq.com/miniprogram/dev/api/media/audio/InnerAudioContext.html
     */
    createInnerAudioContext(): InnerAudioContext;
}

export type CreateInnerAudioContext = () => InnerAudioContext;

export interface InnerAudioContext {
    /**
     * 当前音频的长度（单位：s），只有在当前有合法的 src 时返回
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    duration: number;
    /**
     * 当前音频的播放位置（单位：s），只有在当前有合法的 src 时返回
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    currentTime: number;
    /**
     * 当前是是否暂停或停止状态，true 表示暂停或停止，false 表示正在播放
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    paused: boolean;
    /**
     * 音频的数据链接，用于直接播放
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    src: string;
    /**
     * 音频开始播放的位置（单位：s）
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    startTime: number;
    /**
     * 音频缓冲的时间点，仅保证当前播放时间点到此时间点内容已缓冲
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "x",
                "unixVer": "x",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    buffered: number;
    /**
     * 是否自动开始播放，默认 false
     * @defaultValue false
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    autoplay: boolean;
    /**
     * 是否循环播放，默认 false
     * @defaultValue false
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    loop: boolean;
    /**
     * 是否遵循系统静音开关，当此参数为 false 时，即使用户打开了静音开关，也能继续发出声音，默认值 true
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "x",
                  "unixUtsPlugin": "x"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "x",
                  "unixUtsPlugin": "x"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "x",
                "unixVer": "x",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "x",
              "unixVer": "x"
          }
      }
     */
    obeyMuteSwitch: boolean;
    /**
     * 音量。范围 0~1。
     * @uniPlatform
     {
         "app": {
             "android": {
                 "osVer": "5.0",
                 "uniVer": "√",
                 "uniUtsPlugin": "x",
                 "unixVer": "4.33",
                 "unixUtsPlugin": "4.33"
             },
             "ios": {
                 "osVer": "12.0",
                 "uniVer": "√",
                 "uniUtsPlugin": "x",
                 "unixVer": "4.41",
                 "unixUtsPlugin": "4.41"
             },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
         },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
         "web": {
             "uniVer": "√",
             "unixVer": "4.0"
         }
     }
     */
    volume: number;
    /**
     * 播放的倍率。可取值： 0.5/0.8/1.0/1.25/1.5/2.0，默认值为1.0。（仅 App 支持）
     * @defaultValue 1.0
     * @uniPlatform
     {
         "app": {
             "android": {
                 "osVer": "5.0",
                 "uniVer": "√",
                 "uniUtsPlugin": "x",
                 "unixVer": "4.33",
                 "unixUtsPlugin": "4.33"
             },
             "ios": {
                 "osVer": "12.0",
                 "uniVer": "√",
                 "uniUtsPlugin": "x",
                 "unixVer": "4.41",
                 "unixUtsPlugin": "4.41"
             },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "x",
                "unixVer": "x",
            }
         },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
         "web": {
             "uniVer": "x",
             "unixVer": "x"
         }
     }
     */
    playbackRate?: number;
	/**
	 * 是否缓存线上音频资源，默认值为true，当设置false时，不会缓存资源到本地，直播地址需要主动设置为false
	 * @uniPlatform
	  {
	      "app": {
	          "android": {
	              "osVer": "5.0",
	              "uniVer": "x",
	              "uniUtsPlugin": "x",
	              "unixVer": "4.71",
	              "unixUtsPlugin": "4.71"
	          },
	          "ios": {
	              "osVer": "12.0",
	              "uniVer": "x",
	              "uniUtsPlugin": "x",
	              "unixVer": "4.71",
	              "unixUtsPlugin": "4.71"
	          },
	        "harmony": {
	            "osVer": "5.0.0",
	            "uniVer": "x",
	            "unixVer": "x",
	        }
	      },
	    "mp": {
	        "weixin": {
	            "hostVer": "√",
	            "uniVer": "√",
	            "unixVer": "4.41"
	        },
	        "alipay": {
	            "hostVer": "1.23.4",
	            "uniVer": "√",
	            "unixVer": "x"
	        },
	        "baidu": {
	            "hostVer": "√",
	            "uniVer": "√",
	            "unixVer": "x"
	        },
	        "toutiao": {
	            "hostVer": "√",
	            "uniVer": "√",
	            "unixVer": "x"
	        },
	        "lark": {
	            "hostVer": "√",
	            "uniVer": "√",
	            "unixVer": "x"
	        },
	        "qq": {
	            "hostVer": "√",
	            "uniVer": "√",
	            "unixVer": "x"
	        },
	        "kuaishou": {
	            "hostVer": "√",
	            "uniVer": "√",
	            "unixVer": "x"
	        },
	        "jd": {
	            "hostVer": "√",
	            "uniVer": "√",
	            "unixVer": "x"
	        }
	    },
	      "web": {
	          "uniVer": "√",
	          "unixVer": "4.0"
	      }
	  }
	 */
	// #ifdef APP-IOS || APP-ANDROID
	cache: boolean;
	// #endif
    /**
     * 暂停
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    pause(): void;
    /**
     * 停止
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    stop(): void;
    /**
     * 播放
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    play(): void;
    /**
     * 跳转到指定位置，单位 s
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    seek(position: number): void;
    /**
     * 销毁当前实例
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    destroy(): void;
    /**
     * 音频进入可以播放状态，但不保证后面可以流畅播放
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    onCanplay(callback: (result: any) => void): void;
    /**
     * 音频播放事件
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     * }
     */
    onPlay(callback: (result: any) => void): void;
    /**
     * 音频暂停事件
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     * }
     */
    onPause(callback: (result: any) => void): void;
    /**
     * 音频停止事件
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    onStop(callback: (result: any) => void): void;
    /**
     * 音频自然播放结束事件
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    onEnded(callback: (result: any) => void): void;
    /**
     * 音频播放进度更新事件
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    onTimeUpdate(callback: (result: any) => void): void;
    /**
     * 音频播放错误事件
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    onError(callback: (result: ICreateInnerAudioContextFail) => void): void;
    /**
     * 音频加载中事件，当音频因为数据不足，需要停下来加载时会触发
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    onWaiting(callback: (result: any) => void): void;
    /**
     * 音频进行 seek 操作事件
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    onSeeking(callback: (result: any) => void): void;
    /**
     * 音频完成 seek 操作事件
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    onSeeked(callback: (result: any) => void): void;
    /**
     * 取消监听 onCanplay 事件
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    offCanplay(callback: (result: any) => void): void;
    /**
     * 取消监听 onPlay 事件
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    offPlay(callback: (result: any) => void): void;
    /**
     * 取消监听 onPause 事件
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    offPause(callback: (result: any) => void): void;
    /**
     * 取消监听 onStop 事件
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    offStop(callback: (result: any) => void): void;
    /**
     * 取消监听 onEnded 事件
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    offEnded(callback: (result: any) => void): void;
    /**
     * 取消监听 onTimeUpdate 事件
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    offTimeUpdate(callback: (result: any) => void): void;
    /**
     * 取消监听 onWaiting 事件
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    offError(callback: (result: ICreateInnerAudioContextFail) => void): void;
    /**
     * 取消监听 onWaiting 事件
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    offWaiting(callback: (result: any) => void): void;
    /**
     * 取消监听 onSeeking 事件
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    offSeeking(callback: (result: any) => void): void;
    /**
     * 取消监听 onSeeked 事件
     * @uniPlatform
      {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
        "mp": {
            "weixin": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "4.41"
            },
            "alipay": {
                "hostVer": "1.23.4",
                "uniVer": "√",
                "unixVer": "x"
            },
            "baidu": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "toutiao": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "lark": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "qq": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "kuaishou": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            },
            "jd": {
                "hostVer": "√",
                "uniVer": "√",
                "unixVer": "x"
            }
        },
          "web": {
              "uniVer": "√",
              "unixVer": "4.0"
          }
      }
     */
    offSeeked(callback: (result: any) => void): void;
    /**
     * 需要基础库： `2.13.0`
     *
     * `origin`: 发送完整的referrer; `no-referrer`: 不发送。格式固定为 `https://servicewechat.com/{appid}/{version}/page-frame.html`，其中 {appid} 为小程序的 appid，{version} 为小程序的版本号，版本号为 0 表示为开发版、体验版以及审核版本，版本号为 devtools 表示为开发者工具，其余为正式版本；
     *
     * @uniPlatform {
     *   "mp": {
     *     "weixin": {
     *       "hostVer": "2.13.0",
     *       "uniVer": "√",
     *       "unixVer": "4.41"
     *     },
     *     "alipay": {
     *       "hostVer": "-",
     *       "uniVer": "-",
     *       "unixVer": "-"
     *     },
     *     "baidu": {
     *       "hostVer": "-",
     *       "uniVer": "-",
     *       "unixVer": "-"
     *     },
     *     "toutiao": {
     *       "hostVer": "-",
     *       "uniVer": "-",
     *       "unixVer": "-"
     *     },
     *     "lark": {
     *       "hostVer": "-",
     *       "uniVer": "-",
     *       "unixVer": "-"
     *     },
     *     "qq": {
     *       "hostVer": "-",
     *       "uniVer": "-",
     *       "unixVer": "-"
     *     },
     *     "kuaishou": {
     *       "hostVer": "-",
     *       "uniVer": "-",
     *       "unixVer": "-"
     *     },
     *     "jd": {
     *       "hostVer": "-",
     *       "uniVer": "-",
     *       "unixVer": "-"
     *     }
     *   }
     * }
     */
    referrerPolicy?: string | null;
}

/**
 * 错误码
 */
export type CreateInnerAudioContextErrorCode =
    /**
     * 系统错误
     * @uniPlatform {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
          "web": {
              "uniVer": "x",
              "unixVer": "x"
          }
      }
     */
    1107601 |
    /**
     * 网络错误
     * @uniPlatform {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
          "web": {
              "uniVer": "x",
              "unixVer": "x"
          }
      }
     */
    1107602 |
    /**
     * 文件错误
     * @uniPlatform {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
          "web": {
              "uniVer": "x",
              "unixVer": "x"
          }
      }
     */
    1107603 |
    /**
     * 格式错误
     * @uniPlatform {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
          "web": {
              "uniVer": "x",
              "unixVer": "x"
          }
      }
     */
    1107604 |
    /**
     * 未知错误
     * @uniPlatform {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
          "web": {
              "uniVer": "x",
              "unixVer": "x"
          }
      }
     */
    1107605 |
    /**
     * 播放路径不能为空
     * @uniPlatform {
          "app": {
              "android": {
                  "osVer": "5.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.33",
                  "unixUtsPlugin": "4.33"
              },
              "ios": {
                  "osVer": "12.0",
                  "uniVer": "√",
                  "uniUtsPlugin": "x",
                  "unixVer": "4.41",
                  "unixUtsPlugin": "4.41"
              },
            "harmony": {
                "osVer": "5.0.0",
                "uniVer": "4.23",
                "unixVer": "4.61",
            }
          },
          "web": {
              "uniVer": "x",
              "unixVer": "x"
          }
      }
     */
    1107609;

export interface ICreateInnerAudioContextFail extends IUniError {
    errCode: CreateInnerAudioContextErrorCode
};
