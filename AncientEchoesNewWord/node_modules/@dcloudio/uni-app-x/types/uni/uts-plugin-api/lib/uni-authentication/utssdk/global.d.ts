// 本文件为自动构建生成
import {
  StartSoterAuthentication as StartSoterAuthentication<PERSON><PERSON>in,
  Soter<PERSON>uth<PERSON><PERSON> as SoterAuth<PERSON>ode<PERSON><PERSON><PERSON>,
  StartSoterAuthenticationSuccess as StartSoterAuthenticationSuccessOrigin,
  StartSoterAuthenticationSuccess<PERSON>allback as StartSoterAuthenticationSuccess<PERSON><PERSON>back<PERSON><PERSON><PERSON>,
  StartSoterAuthenticationFail as StartSoterAuthenticationFailOrigin,
  StartSoterAuthenticationFailCallback as StartSoterAuthenticationFailCallbackOrigin,
  StartSoterAuthenticationComplete as StartSoterAuthenticationCompleteOrigin,
  StartSoterAuthenticationCompleteCallback as StartSoterAuthenticationCompleteCallbackOrigin,
  StartSoterAuthenticationOptions as StartSoterAuthenticationOptionsOrigin,
  CheckIsSupportSoterAuthentication as CheckIsSupportSoterAuthenticationOrigin,
  CheckIsSupportSoterAuthenticationSuc<PERSON> as CheckIsSupportSoterAuthenticationSuc<PERSON><PERSON><PERSON><PERSON>,
  CheckIsSupportSoterAuthenticationSuc<PERSON><PERSON><PERSON>back as CheckIsSupportSoterAuthenticationSuccessCallback<PERSON><PERSON><PERSON>,
  CheckIsSupportSoterAuthenticationFail as CheckIsSupportSoterAuthenticationFailOrigin,
  CheckIsSupportSoterAuthenticationFailCallback as CheckIsSupportSoterAuthenticationFailCallbackOrigin,
  CheckIsSupportSoterAuthenticationComplete as CheckIsSupportSoterAuthenticationCompleteOrigin,
  CheckIsSupportSoterAuthenticationCompleteCallback as CheckIsSupportSoterAuthenticationCompleteCallbackOrigin,
  CheckIsSupportSoterAuthenticationOptions as CheckIsSupportSoterAuthenticationOptionsOrigin,
  CheckIsSoterEnrolledInDevice as CheckIsSoterEnrolledInDeviceOrigin,
  CheckIsSoterEnrolledInDeviceSuccess as CheckIsSoterEnrolledInDeviceSuccessOrigin,
  CheckIsSoterEnrolledInDeviceSuccessCallback as CheckIsSoterEnrolledInDeviceSuccessCallbackOrigin,
  CheckIsSoterEnrolledInDeviceFail as CheckIsSoterEnrolledInDeviceFailOrigin,
  CheckIsSoterEnrolledInDeviceFailCallback as CheckIsSoterEnrolledInDeviceFailCallbackOrigin,
  CheckIsSoterEnrolledInDeviceComplete as CheckIsSoterEnrolledInDeviceCompleteOrigin,
  CheckIsSoterEnrolledInDeviceCompleteCallback as CheckIsSoterEnrolledInDeviceCompleteCallbackOrigin,
  CheckIsSoterEnrolledInDeviceOptions as CheckIsSoterEnrolledInDeviceOptionsOrigin,
  Uni as UniOrigin
} from './interface'

declare global {
  type StartSoterAuthentication = StartSoterAuthenticationOrigin
  type SoterAuthMode = SoterAuthModeOrigin
  type StartSoterAuthenticationSuccess = StartSoterAuthenticationSuccessOrigin
  type StartSoterAuthenticationSuccessCallback = StartSoterAuthenticationSuccessCallbackOrigin
  type StartSoterAuthenticationFail = StartSoterAuthenticationFailOrigin
  type StartSoterAuthenticationFailCallback = StartSoterAuthenticationFailCallbackOrigin
  type StartSoterAuthenticationComplete = StartSoterAuthenticationCompleteOrigin
  type StartSoterAuthenticationCompleteCallback = StartSoterAuthenticationCompleteCallbackOrigin
  type StartSoterAuthenticationOptions = StartSoterAuthenticationOptionsOrigin
  type CheckIsSupportSoterAuthentication = CheckIsSupportSoterAuthenticationOrigin
  type CheckIsSupportSoterAuthenticationSuccess = CheckIsSupportSoterAuthenticationSuccessOrigin
  type CheckIsSupportSoterAuthenticationSuccessCallback = CheckIsSupportSoterAuthenticationSuccessCallbackOrigin
  type CheckIsSupportSoterAuthenticationFail = CheckIsSupportSoterAuthenticationFailOrigin
  type CheckIsSupportSoterAuthenticationFailCallback = CheckIsSupportSoterAuthenticationFailCallbackOrigin
  type CheckIsSupportSoterAuthenticationComplete = CheckIsSupportSoterAuthenticationCompleteOrigin
  type CheckIsSupportSoterAuthenticationCompleteCallback = CheckIsSupportSoterAuthenticationCompleteCallbackOrigin
  type CheckIsSupportSoterAuthenticationOptions = CheckIsSupportSoterAuthenticationOptionsOrigin
  type CheckIsSoterEnrolledInDevice = CheckIsSoterEnrolledInDeviceOrigin
  type CheckIsSoterEnrolledInDeviceSuccess = CheckIsSoterEnrolledInDeviceSuccessOrigin
  type CheckIsSoterEnrolledInDeviceSuccessCallback = CheckIsSoterEnrolledInDeviceSuccessCallbackOrigin
  type CheckIsSoterEnrolledInDeviceFail = CheckIsSoterEnrolledInDeviceFailOrigin
  type CheckIsSoterEnrolledInDeviceFailCallback = CheckIsSoterEnrolledInDeviceFailCallbackOrigin
  type CheckIsSoterEnrolledInDeviceComplete = CheckIsSoterEnrolledInDeviceCompleteOrigin
  type CheckIsSoterEnrolledInDeviceCompleteCallback = CheckIsSoterEnrolledInDeviceCompleteCallbackOrigin
  type CheckIsSoterEnrolledInDeviceOptions = CheckIsSoterEnrolledInDeviceOptionsOrigin
  interface Uni extends UniOrigin { }
}
