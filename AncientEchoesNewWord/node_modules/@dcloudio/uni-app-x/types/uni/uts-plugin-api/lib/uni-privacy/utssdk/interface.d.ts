export interface Uni {

  /**
   * 开启监听隐私协议状态改变
   * @uniPlatform 
    {
      "app": {
        "android": {
          "osVer": "5.0",
          "uniVer": "4.31",
          "uniUtsPlugin": "4.31",
          "unixVer": "4.31",
          "unixUtsPlugin": "4.31"
        },
        "ios": {
          "osVer": "12.0",
          "uniVer": "4.31",
          "uniUtsPlugin": "4.31",
          "unixVer": "4.31",
          "unixUtsPlugin": "4.31"
        },
        "harmony": {
          "osVer": "5.0",
          "uniVer": "x",
          "unixVer": "4.61"
        }
      },
      "web": {
        "uniVer": "x",
        "unixVer": "x"
      }
    }
   * @example
    ```typescript
    const id = uni.onPrivacyAuthorizationChange((res) => {

    })
    ```
   */
  onPrivacyAuthorizationChange(callback: OnPrivacyAuthorizationChangeCallback): number

  /**
   * 取消监听隐私协议状态改变
   * @param {number} id 开启监听隐私协议状态改变返回的id
   * @uniPlatform 
    {
      "app": {
        "android": {
          "osVer": "5.0",
          "uniVer": "4.31",
          "uniUtsPlugin": "4.31",
          "unixVer": "4.31",
          "unixUtsPlugin": "4.31"
        },
        "ios": {
          "osVer": "12.0",
          "uniVer": "4.31",
          "uniUtsPlugin": "4.31",
          "unixVer": "4.31",
          "unixUtsPlugin": "4.31"
        },
        "harmony": {
          "osVer": "5.0",
          "uniVer": "x",
          "unixVer": "4.61"
        }
      },
      "web": {
        "uniVer": "x",
        "unixVer": "x"
      }
    }
   * @example
    ```typescript
    uni.offPrivacyAuthorizationChange(id)
    ```
   */
  offPrivacyAuthorizationChange(id: number): void

  /**
   * 获取隐私协议状态
   * @uniPlatform {
   *   "app": {
   *     "android": {
   *       "osVer": "5.0",
   *       "uniVer": "4.31",
   *       "uniUtsPlugin": "4.31",
   *       "unixVer": "4.31",
   *       "unixUtsPlugin": "4.31"
   *     },
   *     "ios": {
   *       "osVer": "12.0",
   *       "uniVer": "4.31",
   *       "uniUtsPlugin": "4.31",
   *       "unixVer": "4.31",
   *       "unixUtsPlugin": "4.31"
   *     },
   *     "harmony": {
   *       "osVer": "5.0",
   *       "uniVer": "x",
   *       "unixVer": "4.61"
   *     }
   *   },
   *   "mp": {
   *     "weixin": {
   *       "hostVer": "2.32.3",
   *       "uniVer": "√",
   *       "unixVer": "4.41"
   *     },
   *     "alipay": {
   *       "hostVer": "√",
   *       "uniVer": "√",
   *       "unixVer": "x"
   *     },
   *     "baidu": {
   *       "hostVer": "√",
   *       "uniVer": "√",
   *       "unixVer": "x"
   *     },
   *     "toutiao": {
   *       "hostVer": "√",
   *       "uniVer": "√",
   *       "unixVer": "x"
   *     },
   *     "lark": {
   *       "hostVer": "√",
   *       "uniVer": "√",
   *       "unixVer": "x"
   *     },
   *     "qq": {
   *       "hostVer": "√",
   *       "uniVer": "√",
   *       "unixVer": "x"
   *     },
   *     "kuaishou": {
   *       "hostVer": "√",
   *       "uniVer": "√",
   *       "unixVer": "x"
   *     },
   *     "jd": {
   *       "hostVer": "√",
   *       "uniVer": "√",
   *       "unixVer": "x"
   *     }
   *   },
   *   "web": {
   *     "uniVer": "x",
   *     "unixVer": "x"
   *   }
   * }
   * @example
    ```typescript
    uni.getPrivacySetting({
      success: (res) => {
        console.log('success')
      },
      fail: (err) => {
        console.log(err)
      },
      complete: (res) => {
        console.log('complete')
      }
    })
    ```
     * @tutorial_weixin https://developers.weixin.qq.com/miniprogram/dev/api/open-api/privacy/wx.getPrivacySetting.html
     */
  getPrivacySetting(options: GetPrivacySettingOptions): void

  /**
   * 重置隐私协议状态为未同意
   * @uniPlatform 
    {
      "app": {
        "android": {
          "osVer": "5.0",
          "uniVer": "4.31",
          "uniUtsPlugin": "4.31",
          "unixVer": "4.31",
          "unixUtsPlugin": "4.31"
        },
        "ios": {
          "osVer": "12.0",
          "uniVer": "4.31",
          "uniUtsPlugin": "4.31",
          "unixVer": "4.31",
          "unixUtsPlugin": "4.31"
        },
        "harmony": {
          "osVer": "5.0",
          "uniVer": "x",
          "unixVer": "4.61"
        }
      },
      "web": {
        "uniVer": "x",
        "unixVer": "x"
      }
    }
   * @example
    ```typescript
    uni.resetPrivacyAuthorization()
    ```
   */
  resetPrivacyAuthorization(): void
}

export type OnPrivacyAuthorizationChange = (callback: OnPrivacyAuthorizationChangeCallback) => number

export type OffPrivacyAuthorizationChange = (id: number) => void

export type OnPrivacyAuthorizationChangeCallback = (res: PrivacyChangeResult) => void

export type PrivacyChangeResult = {
  /**
   * 是否需要用户授权隐私协议(用户之前同意过返回false，没同意过则返回true)
   * @uniPlatform 
    {
      "app": {
        "android": {
          "osVer": "5.0",
          "uniVer": "4.31",
          "uniUtsPlugin": "4.31",
          "unixVer": "4.31",
          "unixUtsPlugin": "4.31"
        },
        "ios": {
          "osVer": "12.0",
          "uniVer": "4.31",
          "uniUtsPlugin": "4.31",
          "unixVer": "4.31",
          "unixUtsPlugin": "4.31"
        },
        "harmony": {
          "osVer": "5.0",
          "uniVer": "x",
          "unixVer": "4.61"
        }
      },
      "web": {
        "uniVer": "x",
        "unixVer": "x"
      }
    }
   */
  needAuthorization: boolean
}

export type GetPrivacySetting = (options: GetPrivacySettingOptions) => void

export type GetPrivacySettingSuccessCallback = (result: GetPrivacySettingSuccessResult) => void;
export type GetPrivacySettingFailCallback = (result: any) => void;
export type GetPrivacySettingCompleteCallback = (result: any) => void;
export type ResetPrivacyAuthorization = () => void

export type GetPrivacySettingSuccessResult = {
  /**
   * 是否需要用户授权隐私协议(用户之前同意过返回false，没同意过则返回true)
   * @uniPlatform 
    {
      "app": {
        "android": {
          "osVer": "5.0",
          "uniVer": "4.31",
          "uniUtsPlugin": "4.31",
          "unixVer": "4.31",
          "unixUtsPlugin": "4.31"
        },
        "ios": {
          "osVer": "12.0",
          "uniVer": "4.31",
          "uniUtsPlugin": "4.31",
          "unixVer": "4.31",
          "unixUtsPlugin": "4.31"
        },
        "harmony": {
          "osVer": "5.0",
          "uniVer": "x",
          "unixVer": "4.61"
        }
      },
      "mp": {
        "weixin": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "4.41"
        },
        "alipay": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "baidu": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "toutiao": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "lark": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "qq": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "kuaishou": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "jd": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        }
      },
      "web": {
        "uniVer": "x",
        "unixVer": "x"
      }
    }
   */
  needAuthorization: boolean
    /**
     * 隐私授权协议的名称
     *
     * @uniPlatform {
     *   "mp": {
     *     "weixin": {
     *       "hostVer": "√",
     *       "uniVer": "√",
     *       "unixVer": "4.41"
     *     },
     *     "alipay": {
     *       "hostVer": "-",
     *       "uniVer": "-",
     *       "unixVer": "-"
     *     },
     *     "baidu": {
     *       "hostVer": "-",
     *       "uniVer": "-",
     *       "unixVer": "-"
     *     },
     *     "toutiao": {
     *       "hostVer": "-",
     *       "uniVer": "-",
     *       "unixVer": "-"
     *     },
     *     "lark": {
     *       "hostVer": "-",
     *       "uniVer": "-",
     *       "unixVer": "-"
     *     },
     *     "qq": {
     *       "hostVer": "-",
     *       "uniVer": "-",
     *       "unixVer": "-"
     *     },
     *     "kuaishou": {
     *       "hostVer": "-",
     *       "uniVer": "-",
     *       "unixVer": "-"
     *     },
     *     "jd": {
     *       "hostVer": "-",
     *       "uniVer": "-",
     *       "unixVer": "-"
     *     }
     *   }
     * }
     */
    privacyContractName?: string | null;
}

export type GetPrivacySettingOptions = {
  /**
   * 接口调用成功的回调函数
   * @defaultValue null
   * @uniPlatform 
    {
      "app": {
        "android": {
          "osVer": "5.0",
          "uniVer": "4.31",
          "uniUtsPlugin": "4.31",
          "unixVer": "4.31",
          "unixUtsPlugin": "4.31"
        },
        "ios": {
          "osVer": "12.0",
          "uniVer": "4.31",
          "uniUtsPlugin": "4.31",
          "unixVer": "4.31",
          "unixUtsPlugin": "4.31"
        },
        "harmony": {
          "osVer": "5.0",
          "uniVer": "x",
          "unixVer": "4.61"
        }
      },
      "mp": {
        "weixin": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "4.41"
        },
        "alipay": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "baidu": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "toutiao": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "lark": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "qq": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "kuaishou": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "jd": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        }
      },
      "web": {
        "uniVer": "x",
        "unixVer": "x"
      }
    }
   */
  success?: GetPrivacySettingSuccessCallback

  /**
   * 接口调用失败的回调函数
   * @defaultValue null
   * @uniPlatform 
    {
      "app": {
        "android": {
          "osVer": "5.0",
          "uniVer": "4.31",
          "uniUtsPlugin": "4.31",
          "unixVer": "4.31",
          "unixUtsPlugin": "4.31"
        },
        "ios": {
          "osVer": "12.0",
          "uniVer": "4.31",
          "uniUtsPlugin": "4.31",
          "unixVer": "4.31",
          "unixUtsPlugin": "4.31"
        },
        "harmony": {
          "osVer": "5.0",
          "uniVer": "x",
          "unixVer": "4.61"
        }
      },
      "mp": {
        "weixin": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "4.41"
        },
        "alipay": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "baidu": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "toutiao": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "lark": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "qq": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "kuaishou": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "jd": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        }
      },
      "web": {
        "uniVer": "x",
        "unixVer": "x"
      }
    }
   */
  fail?: GetPrivacySettingFailCallback

  /**
   * 接口调用结束的回调函数（调用成功、失败都会执行）
   * @defaultValue null
   * @uniPlatform 
    {
      "app": {
        "android": {
          "osVer": "5.0",
          "uniVer": "4.31",
          "uniUtsPlugin": "4.31",
          "unixVer": "4.31",
          "unixUtsPlugin": "4.31"
        },
        "ios": {
          "osVer": "12.0",
          "uniVer": "4.31",
          "uniUtsPlugin": "4.31",
          "unixVer": "4.31",
          "unixUtsPlugin": "4.31"
        },
        "harmony": {
          "osVer": "5.0",
          "uniVer": "x",
          "unixVer": "4.61"
        }
      },
      "mp": {
        "weixin": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "4.41"
        },
        "alipay": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "baidu": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "toutiao": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "lark": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "qq": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "kuaishou": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        },
        "jd": {
          "hostVer": "√",
          "uniVer": "√",
          "unixVer": "x"
        }
      },
      "web": {
        "uniVer": "x",
        "unixVer": "x"
      }
    }
   */
  complete?: GetPrivacySettingCompleteCallback
}
