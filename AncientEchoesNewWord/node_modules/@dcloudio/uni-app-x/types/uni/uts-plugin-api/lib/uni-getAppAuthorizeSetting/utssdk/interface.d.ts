export interface Uni {
    /**
      * 获取 APP 授权设置。
      * @tutorial https://doc.dcloud.net.cn/uni-app-x/api/get-app-authorize-setting.html
      * @tutorial_uni_app_x https://doc.dcloud.net.cn/uni-app-x/api/get-app-authorize-setting.html
      * @tutorial_uni_app https://uniapp.dcloud.net.cn/api/system/getappauthorizesetting.html
      * @uniPlatform {
      *   "app": {
      *     "android": {
      *       "osVer": "5.0",
      *       "uniVer": "√",
      *       "unixVer": "3.9+"
      *     },
      *     "ios": {
      *       "osVer": "12.0",
      *       "uniVer": "√",
      *       "unixVer": "4.11"
      *     },
      *     "harmony": {
      *       "osVer": "3.0",
      *       "uniVer": "4.31",
      *       "unixVer": "4.61"
      *     }
      *   },
      *   "mp": {
      *     "weixin": {
      *       "hostVer": "2.20.1",
      *       "uniVer": "√",
      *       "unixVer": "4.41"
      *     },
      *     "alipay": {
      *       "hostVer": "√",
      *       "uniVer": "√",
      *       "unixVer": "x"
      *     },
      *     "baidu": {
      *       "hostVer": "√",
      *       "uniVer": "√",
      *       "unixVer": "x"
      *     },
      *     "toutiao": {
      *       "hostVer": "√",
      *       "uniVer": "√",
      *       "unixVer": "x"
      *     },
      *     "lark": {
      *       "hostVer": "√",
      *       "uniVer": "√",
      *       "unixVer": "x"
      *     },
      *     "qq": {
      *       "hostVer": "√",
      *       "uniVer": "√",
      *       "unixVer": "x"
      *     },
      *     "kuaishou": {
      *       "hostVer": "√",
      *       "uniVer": "√",
      *       "unixVer": "x"
      *     },
      *     "jd": {
      *       "hostVer": "√",
      *       "uniVer": "√",
      *       "unixVer": "x"
      *     }
      *   },
      *   "web": {
      *     "uniVer": "x",
      *     "unixVer": "x"
      *   }
      * }
      * @example
       ```typescript
        uni.getAppAuthorizeSetting()
       ```
     * @tutorial_weixin https://developers.weixin.qq.com/miniprogram/dev/api/base/system/wx.getAppAuthorizeSetting.html
     */
    getAppAuthorizeSetting(): GetAppAuthorizeSettingResult;
}


export type GetAppAuthorizeSetting = () => GetAppAuthorizeSettingResult;
export type GetAppAuthorizeSettingResult = {
    /**
     * 允许 App 使用相册的开关
     *
     * @uniPlatform
     * {
     * 	"app": {
     * 		"android": {
     * 			"osVer": "5.0",
     * 			"uniVer": "x",
     * 			"unixVer": "4.25"
     * 		},
     * 		"ios": {
     * 			"osVer": "12.0",
     * 			"uniVer": "√",
     * 			"unixVer": "4.11"
     * 		}
     * 	}
     * }
     */
    albumAuthorized:
    /**
     * 已经获得授权，无需再次请求授权
     */
    'authorized' |
    /**
     * 请求授权被拒绝，无法再次请求授权。Android平台：需要申请相册相关权限；iOS平台：此情况需要引导用户打开系统设置，在设置页中打开权限
     */
    'denied' |
    /**
     *  尚未请求授权，会在App下一次调用系统相应权限时请求；（仅 iOS 会出现。此种情况下引导用户打开系统设置，不展示开关）
     */
    'not determined' |
    /**
     * Android平台：表示没有配置[相册相关权限](https://doc.dcloud.net.cn/uni-app-x/native/permission/android_permission_adapter.html)，[权限配置详情](https://uniapp.dcloud.net.cn/tutorial/app-nativeresource-android.html#permissions)；iOS平台：当前应用没有配置相册权限描述
     */
    'config error',
    /**
     * 允许 App 使用蓝牙的开关
     *
     * @uniPlatform
     * {
     * 	"app": {
     * 		"android": {
     * 			"osVer": "5.0",
     * 			"uniVer": "x",
     * 			"unixVer": "4.25"
     * 		},
     * 		"ios": {
     * 			"osVer": "12.0",
     * 			"uniVer": "√",
     * 			"unixVer": "4.11"
     * 		}
     * 	}
     * }
     */
    bluetoothAuthorized:
    /**
     * 已经获得授权，无需再次请求授权
     */
    'authorized' |
    /**
     * 请求授权被拒绝，无法再次请求授权。Android平台：需要申请相册相关权限；iOS平台：此情况需要引导用户打开系统设置，在设置页中打开权限
     */
    'denied' |
    /**
     *  尚未请求授权，会在App下一次调用系统相应权限时请求；（仅 iOS 会出现。此种情况下引导用户打开系统设置，不展示开关）
     */
    'not determined' |
    /**
     * Android平台：表示没有配置[蓝牙相关权限](https://doc.dcloud.net.cn/uni-app-x/native/permission/android_permission_adapter.html)，[权限配置详情](https://uniapp.dcloud.net.cn/tutorial/app-nativeresource-android.html#permissions)；iOS平台：当前应用没有配置蓝牙权限描述
     */
    'config error',
    /**
     * 允许 App 使用摄像头的开关
     *
     * @uniPlatform
     * {
     * 	"app": {
     * 		"android": {
     * 			"osVer": "4.4",
     * 			"uniVer": "√",
     * 			"unixVer": "3.9"
     * 		},
     * 		"ios": {
     * 			"osVer": "12.0",
     * 			"uniVer": "√",
     * 			"unixVer": "4.11"
     * 		}
     * 	}
     * }
     */
    cameraAuthorized:
    /**
     * 已经获得授权，无需再次请求授权
     */
    'authorized' |
    /**
     * 请求授权被拒绝，无法再次请求授权。Android平台：需要申请相册相关权限；iOS平台：此情况需要引导用户打开系统设置，在设置页中打开权限
     */
    'denied' |
    /**
     *  尚未请求授权，会在App下一次调用系统相应权限时请求；（仅 iOS 会出现。此种情况下引导用户打开系统设置，不展示开关）
     */
    'not determined' |
    /**
     * Android平台：表示没有配置 `android.permission.CAMERA` 权限，[权限配置详情](https://uniapp.dcloud.net.cn/tutorial/app-nativeresource-android.html#permissions)；iOS平台：当前应用没有配置相机权限描述
     */
    'config error',
    /**
     * 允许 App 使用定位的开关
     *
     * @uniPlatform
     * {
     * 	"app": {
     * 		"android": {
     * 			"osVer": "4.4",
     * 			"uniVer": "√",
     * 			"unixVer": "3.9"
     * 		},
     * 		"ios": {
     * 			"osVer": "12.0",
     * 			"uniVer": "√",
     * 			"unixVer": "4.11"
     * 		}
     * 	}
     * }
     */
    locationAuthorized:
    /**
     * 已经获得授权，无需再次请求授权
     */
    'authorized' |
    /**
     * 请求授权被拒绝，无法再次请求授权。Android平台：需要申请相册相关权限；iOS平台：此情况需要引导用户打开系统设置，在设置页中打开权限
     */
    'denied' |
    /**
     *  尚未请求授权，会在App下一次调用系统相应权限时请求；（仅 iOS 会出现。此种情况下引导用户打开系统设置，不展示开关）
     */
    'not determined' |
    /**
     * Android平台：表示没有配置 `android.permission.ACCESS_COARSE_LOCATION` 权限，[权限配置详情](https://uniapp.dcloud.net.cn/tutorial/app-nativeresource-android.html#permissions)；iOS平台：当前应用没有配置定位权限描述
     */
    'config error',
    /**
     * 定位准确度。
     *
     * @uniPlatform
     * {
     * 	"app": {
     * 		"android": {
     * 			"osVer": "4.4",
     * 			"uniVer": "√",
     * 			"unixVer": "3.9"
     * 		},
     * 		"ios": {
     * 			"osVer": "12.0",
     * 			"uniVer": "√",
     * 			"unixVer": "4.11"
     * 		}
     * 	}
     * }
     */
    locationAccuracy?:
    /**
     * 模糊定位
     */
    'reduced' |
    /**
     * 精准定位
     */
    'full' |
    /**
     * 不支持（包括用户拒绝定位权限和没有包含定位权限描述）
     */
    'unsupported' | null,
    /**
     * 定位准确度（推荐使用 locationAccuracy 属性）。true 表示模糊定位，false 表示精确定位（仅 iOS 支持）
     * @type boolean
     * @uniPlatform
     * {
     * 	"app": {
     * 		"android": {
     * 			"osVer": "x",
     * 			"uniVer": "x",
     * 			"unixVer": "x"
     * 		},
     * 		"ios": {
     * 			"osVer": "12.0",
     * 			"uniVer": "√",
     * 			"unixVer": "4.11"
     * 		}
     * 	}
     * }
     */
    locationReducedAccuracy?: boolean | null,
    /**
     * 允许 App 使用麦克风的开关
     *
     * @uniPlatform
     * {
     * 	"app": {
     * 		"android": {
     * 			"osVer": "4.4",
     * 			"uniVer": "√",
     * 			"unixVer": "3.9"
     * 		},
     * 		"ios": {
     * 			"osVer": "12.0",
     * 			"uniVer": "√",
     * 			"unixVer": "4.11"
     * 		}
     * 	}
     * }
     */
    microphoneAuthorized:
    /**
     * 已经获得授权，无需再次请求授权
     */
    'authorized' |
    /**
     * 请求授权被拒绝，无法再次请求授权。Android平台：需要申请相册相关权限；iOS平台：此情况需要引导用户打开系统设置，在设置页中打开权限
     */
    'denied' |
    /**
     *  尚未请求授权，会在App下一次调用系统相应权限时请求；（仅 iOS 会出现。此种情况下引导用户打开系统设置，不展示开关）
     */
    'not determined' |
    /**
     * Android平台：表示没有配置 `android.permission.RECORD_AUDIO` 权限，[权限配置详情](https://uniapp.dcloud.net.cn/tutorial/app-nativeresource-android.html#permissions)；iOS平台：当前应用没有配置麦克风权限描述
     */
    'config error',
    /**
     * 允许 App 通知的开关
     *
     * @uniPlatform
     * {
     * 	"app": {
     * 		"android": {
     * 			"osVer": "4.4",
     * 			"uniVer": "√",
     * 			"unixVer": "3.9"
     * 		},
     * 		"ios": {
     * 			"osVer": "12.0",
     * 			"uniVer": "√",
     * 			"unixVer": "4.11"
     * 		}
     * 	}
     * }
     */
    notificationAuthorized:
    /**
     * 已经获得授权，无需再次请求授权
     */
    'authorized' |
    /**
     * 请求授权被拒绝，无法再次请求授权。Android平台：需要申请相册相关权限；iOS平台：此情况需要引导用户打开系统设置，在设置页中打开权限
     */
    'denied' |
    /**
     *  尚未请求授权，会在App下一次调用系统相应权限时请求；（仅 iOS 会出现。此种情况下引导用户打开系统设置，不展示开关）
     */
    'not determined' |
    /**
     * Android平台没有该值；iOS平台：没有包含推送权限描述
     */
    'config error',
    /**
     * 允许 App 通知带有提醒的开关（仅 iOS 支持）
     *
     * @uniPlatform
     * {
     * 	"app": {
     * 		"android": {
     * 			"osVer": "x",
     * 			"uniVer": "x",
     * 			"unixVer": "x"
     * 		},
     * 		"ios": {
     * 			"osVer": "12.0",
     * 			"uniVer": "√",
     * 			"unixVer": "4.11"
     * 		}
     * 	}
     * }
     */
    notificationAlertAuthorized?:
    /**
     * 已经获得授权，无需再次请求授权
     */
    'authorized' |
    /**
     * 请求授权被拒绝，无法再次请求授权。Android平台：需要申请相册相关权限；iOS平台：此情况需要引导用户打开系统设置，在设置页中打开权限
     */
    'denied' |
    /**
     *  尚未请求授权，会在App下一次调用系统相应权限时请求；（仅 iOS 会出现。此种情况下引导用户打开系统设置，不展示开关）
     */
    'not determined' |
    /**
     * 当前应用没有配置推送权限描述
     */
    'config error' | null,
    /**
     * 允许 App 通知带有标记的开关（仅 iOS 支持）
     *
     * @uniPlatform
     * {
     * 	"app": {
     * 		"android": {
     * 			"osVer": "x",
     * 			"uniVer": "x",
     * 			"unixVer": "x"
     * 		},
     * 		"ios": {
     * 			"osVer": "12.0",
     * 			"uniVer": "√",
     * 			"unixVer": "4.11"
     * 		}
     * 	}
     * }
     */
    notificationBadgeAuthorized?:
    /**
     * 已经获得授权，无需再次请求授权
     */
    'authorized' |
    /**
     * 请求授权被拒绝，无法再次请求授权。Android平台：需要申请相册相关权限；iOS平台：此情况需要引导用户打开系统设置，在设置页中打开权限
     */
    'denied' |
    /**
     *  尚未请求授权，会在App下一次调用系统相应权限时请求；（仅 iOS 会出现。此种情况下引导用户打开系统设置，不展示开关）
     */
    'not determined' |
    /**
     * 当前应用没有配置推送权限描述
     */
    'config error' | null,
    /**
     * 允许 App 通知带有声音的开关（仅 iOS 支持）
     *
     * @uniPlatform
     * {
     * 	"app": {
     * 		"android": {
     * 			"osVer": "x",
     * 			"uniVer": "x",
     * 			"unixVer": "x"
     * 		},
     * 		"ios": {
     * 			"osVer": "12.0",
     * 			"uniVer": "√",
     * 			"unixVer": "4.11"
     * 		}
     * 	}
     * }
     */
    notificationSoundAuthorized?:
    /**
     * 已经获得授权，无需再次请求授权
     */
    'authorized' |
    /**
     * 请求授权被拒绝，无法再次请求授权。Android平台：需要申请相册相关权限；iOS平台：此情况需要引导用户打开系统设置，在设置页中打开权限
     */
    'denied' |
    /**
     *  尚未请求授权，会在App下一次调用系统相应权限时请求；（仅 iOS 会出现。此种情况下引导用户打开系统设置，不展示开关）
     */
    'not determined' |
    /**
     * 当前应用没有配置推送权限描述
     */
    'config error' | null,
    /**
     * 允许读写日历的开关（仅微信小程序支持）
     *
     * @uniPlatform
     * {
     * 	"app": {
     * 		"android": {
     * 			"osVer": "x",
     * 			"uniVer": "x",
     * 			"unixVer": "x"
     * 		},
     * 		"ios": {
     * 			"osVer": "x",
     * 			"uniVer": "x",
     * 			"unixVer": "x"
     * 		},
       *    "harmony": {
       *      "osVer": "x",
       *      "uniVer": "x",
       *      "unixVer": "x"
       *    }
     * 	}
     * }
     */
    phoneCalendarAuthorized?:
    /**
     * 已经获得授权，无需再次请求授权
     */
    'authorized' |
    /**
     * 请求授权被拒绝，无法再次请求授权。Android平台：需要申请相册相关权限；iOS平台：此情况需要引导用户打开系统设置，在设置页中打开权限
     */
    'denied' |
    /**
     *  尚未请求授权，会在App下一次调用系统相应权限时请求；
     */
    'not determined' |
    /**
     * 当前应用没有配置推送权限描述
     */
    'config error' | null,
    /**
     * 允许读日历的开关（仅鸿蒙支持）
     *
     * @uniPlatform
     * {
     * 	"app": {
     * 		"android": {
     * 			"osVer": "x",
     * 			"uniVer": "x",
     * 			"unixVer": "x"
     * 		},
     * 		"ios": {
     * 			"osVer": "x",
     * 			"uniVer": "x",
     * 			"unixVer": "x"
     * 		},
       *    "harmony": {
       *      "osVer": "5.0.0",
       *      "uniVer": "4.61",
       *      "unixVer": "4.61"
       *    }
     * 	}
     * }
     */
    readPhoneCalendarAuthorized?:
    /**
     * 已经获得授权，无需再次请求授权
     */
    'authorized' |
    /**
     * 请求授权被拒绝，无法再次请求授权。Android平台：需要申请相册相关权限；iOS平台：此情况需要引导用户打开系统设置，在设置页中打开权限
     */
    'denied' |
    /**
     *  尚未请求授权，会在App下一次调用系统相应权限时请求；
     */
    'not determined' |
    /**
     * 当前应用没有配置推送权限描述
     */
    'config error' | null,
    /**
     * 允许写日历的开关（仅鸿蒙支持）
     *
     * @uniPlatform
     * {
     * 	"app": {
     * 		"android": {
     * 			"osVer": "x",
     * 			"uniVer": "x",
     * 			"unixVer": "x"
     * 		},
     * 		"ios": {
     * 			"osVer": "x",
     * 			"uniVer": "x",
     * 			"unixVer": "x"
     * 		},
       *    "harmony": {
       *      "osVer": "5.0.0",
       *      "uniVer": "4.61",
       *      "unixVer": "4.61"
       *    }
     * 	}
     * }
     */
    writePhoneCalendarAuthorized?:
    /**
     * 已经获得授权，无需再次请求授权
     */
    'authorized' |
    /**
     * 请求授权被拒绝，无法再次请求授权；（此情况需要引导用户打开系统设置，在设置页中打开权限）
     */
    'denied' |
    /**
     * 尚未请求授权，会在App下一次调用系统相应权限时请求；
     */
    'not determined' | null,
    /**
     * 允许读取剪切版（仅鸿蒙支持）
     *
     * @uniPlatform
     * {
     * 	"app": {
     * 		"android": {
     * 			"osVer": "x",
     * 			"uniVer": "x",
     * 			"unixVer": "x"
     * 		},
     * 		"ios": {
     * 			"osVer": "x",
     * 			"uniVer": "x",
     * 			"unixVer": "x"
     * 		},
       *    "harmony": {
       *      "osVer": "5.0.0",
       *      "uniVer": "4.61",
       *      "unixVer": "4.61"
       *    }
     * 	}
     * }
     */
    pasteboardAuthorized?:
    /**
     * 已经获得授权，无需再次请求授权
     */
    'authorized' |
    /**
     * 请求授权被拒绝，无法再次请求授权；（此情况需要引导用户打开系统设置，在设置页中打开权限）
     */
    'denied' |
    /**
     * 尚未请求授权，会在App下一次调用系统相应权限时请求；
     */
    'not determined' | null
};
