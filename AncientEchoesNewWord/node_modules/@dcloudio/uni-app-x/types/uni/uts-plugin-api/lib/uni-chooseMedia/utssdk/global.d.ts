// 本文件为自动构建生成
import {
  ChooseMediaErrorCode as ChooseMediaErrorCodeOrigin,
  IChooseMediaError as IChooseMediaErrorOrigin,
  ChooseMediaFileType as ChooseMediaFileTypeOrigin,
  ChooseMediaTempFile as ChooseMediaTempFileOrigin,
  ChooseMediaSuccess as ChooseMediaSuccessOrigin,
  ChooseMediaFail as ChooseMediaFailOrigin,
  ChooseMediaSuccessCallback as ChooseMediaSuccessCallbackOrigin,
  ChooseMediaFailCallback as ChooseMediaFailCallbackOrigin,
  ChooseMediaCompleteCallback as ChooseMediaCompleteCallbackOrigin,
  ChooseMediaPageOrientation as ChooseMediaPageOrientationOrigin,
  ChooseMediaOptions as ChooseMediaOptionsOrigin,
  ChooseMedia as ChooseMediaOrigin,
  Uni as UniOrigin
} from './interface'

declare global {
  type ChooseMediaErrorCode = ChooseMediaErrorCodeOrigin
  type IChooseMediaError = IChooseMediaErrorOrigin
  type ChooseMediaFileType = ChooseMediaFileTypeOrigin
  type ChooseMediaTempFile = ChooseMediaTempFileOrigin
  type ChooseMediaSuccess = ChooseMediaSuccessOrigin
  type ChooseMediaFail = ChooseMediaFailOrigin
  type ChooseMediaSuccessCallback = ChooseMediaSuccessCallbackOrigin
  type ChooseMediaFailCallback = ChooseMediaFailCallbackOrigin
  type ChooseMediaCompleteCallback = ChooseMediaCompleteCallbackOrigin
  type ChooseMediaPageOrientation = ChooseMediaPageOrientationOrigin
  type ChooseMediaOptions = ChooseMediaOptionsOrigin
  type ChooseMedia = ChooseMediaOrigin
  interface Uni extends UniOrigin { }
}
