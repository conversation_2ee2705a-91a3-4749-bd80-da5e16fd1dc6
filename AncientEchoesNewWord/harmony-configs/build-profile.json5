{"app": {"signingConfigs": [{"name": "default", "type": "HarmonyOS", "material": {"storePassword": "00000019350D4E7C95A55A5FBEF0F5AF0AC08D9C030BD36456ED60DA7143472A9AF617AD8A18F20233", "certpath": "./config/io.dcloud.uniappx.cer", "keyAlias": "debugKey", "keyPassword": "0000001958D675423AB2CAB19457483AD81FFBEFD3615C8404D1FB75AE216154CD780B356AF3D0432C", "profile": "./config/hello-uni-app-x.p7b", "signAlg": "SHA256withECDSA", "storeFile": "./config/hello-uni-app-x.p12"}}, {"name": "release", "type": "HarmonyOS", "material": {"storePassword": "00000019350D4E7C95A55A5FBEF0F5AF0AC08D9C030BD36456ED60DA7143472A9AF617AD8A18F20233", "certpath": "./config/release/io.dcloud.uniappx.cer", "keyAlias": "debugKey", "keyPassword": "0000001958D675423AB2CAB19457483AD81FFBEFD3615C8404D1FB75AE216154CD780B356AF3D0432C", "profile": "./config/release/io.dcloud.uniappx.p7b", "signAlg": "SHA256withECDSA", "storeFile": "./config/hello-uni-app-x.p12"}}], "products": [{"name": "default", "signingConfig": "default", "compatibleSdkVersion": "5.0.1(13)", "compatibleSdkVersionStage": "beta6", "runtimeOS": "HarmonyOS", "buildOption": {"strictMode": {"caseSensitiveCheck": true, "useNormalizedOHMUrl": true}}}, {"name": "release", "signingConfig": "release", "compatibleSdkVersion": "5.0.1(13)", "compatibleSdkVersionStage": "beta6", "runtimeOS": "HarmonyOS", "buildOption": {"strictMode": {"caseSensitiveCheck": true, "useNormalizedOHMUrl": true}}}], "buildModeSet": [{"name": "debug"}, {"name": "release"}]}, "modules": [{"name": "entry", "srcPath": "./entry", "targets": [{"name": "default", "applyToProducts": ["default", "release"]}]}]}